#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库系统使用示例
演示各种常见的使用场景和操作方法
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import json

# 添加tools目录到Python路径
sys.path.append(str(Path(__file__).parent.parent / "tools"))

from knowledge_manager import KnowledgeManager


def example_basic_operations():
    """示例1: 基本操作 - 增删改查"""
    print("=== 示例1: 基本操作 ===")
    
    # 初始化管理器
    manager = KnowledgeManager()
    
    # 创建示例数据
    sample_item = {
        "id": "example_basic_001",
        "title": "示例：基本操作演示",
        "description": "这是一个演示基本操作的示例条目",
        "category": "process",
        "subcategory": "example",
        "tags": ["example", "demo", "basic_operations"],
        "content": {
            "summary": "演示知识库的基本增删改查操作",
            "details": "本示例展示了如何使用知识库管理器进行基本的数据操作，包括添加、查询、更新和删除知识条目。",
            "examples": [
                "添加新的知识条目",
                "查询现有条目",
                "更新条目内容",
                "删除不需要的条目"
            ]
        },
        "metadata": {
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "version": "1.0.0",
            "author": "示例脚本",
            "source": "usage_examples.py",
            "confidence_level": "high",
            "review_status": "approved"
        },
        "relationships": {
            "related_items": [],
            "prerequisites": [],
            "follow_ups": []
        }
    }
    
    # 1. 添加条目
    print("1. 添加知识条目...")
    success = manager.add_knowledge_item(sample_item)
    print(f"   添加结果: {'成功' if success else '失败'}")
    
    # 2. 查询条目
    print("2. 查询知识条目...")
    retrieved_item = manager.get_knowledge_item("example_basic_001")
    if retrieved_item:
        print(f"   找到条目: {retrieved_item['title']}")
        print(f"   描述: {retrieved_item['description']}")
    else:
        print("   未找到条目")
    
    # 3. 更新条目
    print("3. 更新知识条目...")
    updates = {
        "title": "示例：基本操作演示（已更新）",
        "tags": ["example", "demo", "basic_operations", "updated"]
    }
    success = manager.update_knowledge_item("example_basic_001", updates)
    print(f"   更新结果: {'成功' if success else '失败'}")
    
    # 4. 验证更新
    updated_item = manager.get_knowledge_item("example_basic_001")
    if updated_item:
        print(f"   更新后标题: {updated_item['title']}")
        print(f"   更新后标签: {updated_item['tags']}")
    
    # 5. 删除条目
    print("5. 删除知识条目...")
    success = manager.delete_knowledge_item("example_basic_001")
    print(f"   删除结果: {'成功' if success else '失败'}")
    
    print()


def example_search_operations():
    """示例2: 搜索操作"""
    print("=== 示例2: 搜索操作 ===")
    
    manager = KnowledgeManager()
    
    # 1. 基本搜索
    print("1. 基本搜索 - 查找'订单处理'相关内容...")
    results = manager.search_knowledge("订单处理")
    print(f"   找到 {len(results)} 个结果:")
    for result in results[:3]:  # 只显示前3个结果
        print(f"   - {result['id']}: {result['title']}")
    
    # 2. 分类搜索
    print("\n2. 分类搜索 - 查找流程规范类别的内容...")
    results = manager.search_knowledge("", category="process")
    print(f"   找到 {len(results)} 个流程规范:")
    for result in results[:3]:
        print(f"   - {result['id']}: {result['title']}")
    
    # 3. 标签搜索
    print("\n3. 标签搜索 - 查找带有'urgent'标签的内容...")
    results = manager.search_knowledge("", tags=["urgent"])
    print(f"   找到 {len(results)} 个紧急事项:")
    for result in results:
        print(f"   - {result['id']}: {result['title']}")
    
    # 4. 组合搜索
    print("\n4. 组合搜索 - 查找司机相关的违规案例...")
    results = manager.search_knowledge(
        "司机",
        category="violations",
        tags=["tardiness"]
    )
    print(f"   找到 {len(results)} 个相关案例:")
    for result in results:
        print(f"   - {result['id']}: {result['title']}")
    
    print()


def example_data_validation():
    """示例3: 数据验证"""
    print("=== 示例3: 数据验证 ===")
    
    manager = KnowledgeManager()
    
    # 1. 验证所有条目
    print("1. 验证所有知识条目...")
    validation_result = manager.validate_all_items()
    print(f"   有效条目: {len(validation_result['valid'])}")
    print(f"   无效条目: {len(validation_result['invalid'])}")
    
    if validation_result['invalid']:
        print("   无效条目列表:")
        for invalid_id in validation_result['invalid']:
            print(f"   - {invalid_id}")
    
    # 2. 验证单个条目
    print("\n2. 验证单个条目...")
    
    # 创建一个有效的条目
    valid_item = {
        "id": "validation_test_001",
        "title": "验证测试条目",
        "description": "用于测试数据验证功能",
        "category": "process",
        "tags": ["validation", "test"],
        "content": {
            "summary": "测试摘要",
            "details": "测试详细内容"
        },
        "metadata": {
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "version": "1.0.0",
            "author": "测试用户"
        }
    }
    
    is_valid = manager.validator.validate_item(valid_item)
    print(f"   有效条目验证结果: {'通过' if is_valid else '失败'}")
    
    # 创建一个无效的条目（缺少必需字段）
    invalid_item = {
        "id": "validation_test_002",
        "title": "无效条目",
        # 缺少description字段
        "category": "process"
        # 缺少其他必需字段
    }
    
    is_valid = manager.validator.validate_item(invalid_item)
    print(f"   无效条目验证结果: {'通过' if is_valid else '失败'}")
    if not is_valid:
        errors = manager.validator.get_validation_errors()
        print("   验证错误:")
        for error in errors:
            print(f"   - {error}")
    
    print()


def example_statistics_and_analysis():
    """示例4: 统计和分析"""
    print("=== 示例4: 统计和分析 ===")
    
    manager = KnowledgeManager()
    
    # 1. 获取基本统计信息
    print("1. 知识库统计信息...")
    stats = manager.search_engine.get_statistics()
    
    print(f"   总条目数: {stats['total_items']}")
    print("   分类统计:")
    for category, count in stats['categories'].items():
        print(f"   - {category}: {count} 个条目")
    
    print("   热门标签:")
    sorted_tags = sorted(stats['tags'].items(), key=lambda x: x[1], reverse=True)
    for tag, count in sorted_tags[:5]:  # 显示前5个热门标签
        print(f"   - {tag}: {count} 次使用")
    
    # 2. 最近更新的条目
    print("\n2. 最近更新的条目:")
    for item in stats['recent_updates'][:5]:  # 显示最近5个更新
        print(f"   - {item['id']}: {item['title']} ({item['updated_at']})")
    
    # 3. 列出所有条目
    print("\n3. 所有条目概览:")
    all_items = manager.list_all_items()
    print(f"   共有 {len(all_items)} 个条目:")
    
    # 按分类分组显示
    by_category = {}
    for item in all_items:
        category = item['category']
        if category not in by_category:
            by_category[category] = []
        by_category[category].append(item)
    
    for category, items in by_category.items():
        print(f"   {category} ({len(items)} 个):")
        for item in items[:3]:  # 每个分类只显示前3个
            print(f"     - {item['id']}: {item['title']}")
        if len(items) > 3:
            print(f"     ... 还有 {len(items) - 3} 个条目")
    
    print()


def example_batch_operations():
    """示例5: 批量操作"""
    print("=== 示例5: 批量操作 ===")
    
    manager = KnowledgeManager()
    
    # 1. 批量添加条目
    print("1. 批量添加示例条目...")
    
    batch_items = []
    for i in range(3):
        item = {
            "id": f"batch_example_{i+1:03d}",
            "title": f"批量示例条目 {i+1}",
            "description": f"这是第 {i+1} 个批量创建的示例条目",
            "category": "faq",
            "tags": ["batch", "example", f"item_{i+1}"],
            "content": {
                "summary": f"批量示例 {i+1} 的摘要",
                "details": f"批量示例 {i+1} 的详细内容"
            },
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "version": "1.0.0",
                "author": "批量操作脚本"
            }
        }
        batch_items.append(item)
    
    # 添加条目
    success_count = 0
    for item in batch_items:
        if manager.add_knowledge_item(item):
            success_count += 1
    
    print(f"   成功添加 {success_count}/{len(batch_items)} 个条目")
    
    # 2. 批量搜索和处理
    print("\n2. 批量搜索和处理...")
    
    # 搜索所有批量创建的条目
    batch_results = manager.search_knowledge("", tags=["batch"])
    print(f"   找到 {len(batch_results)} 个批量条目")
    
    # 批量更新标签
    print("   批量更新标签...")
    update_count = 0
    for result in batch_results:
        item_id = result['id']
        current_tags = result.get('tags', [])
        new_tags = current_tags + ['batch_updated']
        
        if manager.update_knowledge_item(item_id, {'tags': new_tags}):
            update_count += 1
    
    print(f"   成功更新 {update_count} 个条目的标签")
    
    # 3. 批量删除
    print("\n3. 批量删除示例条目...")
    delete_count = 0
    for result in batch_results:
        if manager.delete_knowledge_item(result['id']):
            delete_count += 1
    
    print(f"   成功删除 {delete_count} 个条目")
    
    print()


def example_advanced_search():
    """示例6: 高级搜索功能"""
    print("=== 示例6: 高级搜索功能 ===")
    
    manager = KnowledgeManager()
    
    # 1. 相关条目推荐
    print("1. 相关条目推荐...")
    
    # 找一个存在的条目
    all_items = manager.list_all_items()
    if all_items:
        sample_item_id = all_items[0]['id']
        print(f"   基于条目 '{sample_item_id}' 的相关推荐:")
        
        related_items = manager.search_engine.get_related_items(sample_item_id, limit=3)
        for item in related_items:
            print(f"   - {item['id']}: {item['title']}")
    
    # 2. 复杂过滤搜索
    print("\n2. 复杂过滤搜索...")
    
    # 搜索高可信度的流程规范
    results = manager.search_knowledge(
        "",
        category="process",
        confidence_level="high"
    )
    print(f"   高可信度的流程规范: {len(results)} 个")
    
    # 3. 搜索性能测试
    print("\n3. 搜索性能测试...")
    
    import time
    
    # 测试搜索性能
    start_time = time.time()
    for _ in range(10):
        results = manager.search_knowledge("订单")
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 10
    print(f"   平均搜索时间: {avg_time:.3f} 秒")
    
    print()


def main():
    """主函数 - 运行所有示例"""
    print("🚀 知识库系统使用示例")
    print("=" * 50)
    
    try:
        # 运行各个示例
        example_basic_operations()
        example_search_operations()
        example_data_validation()
        example_statistics_and_analysis()
        example_batch_operations()
        example_advanced_search()
        
        print("✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
