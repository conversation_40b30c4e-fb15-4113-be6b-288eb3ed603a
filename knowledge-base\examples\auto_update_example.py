#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能化知识库自动更新示例
演示如何使用自动更新系统处理WhatsApp群组聊天记录
"""

import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime

# 添加tools目录到Python路径
sys.path.append(str(Path(__file__).parent.parent / "tools"))

from auto_update_manager import AutoUpdateManager
from whatsapp_parser import WhatsAppMessageParser
from gemini_integration import GeminiKnowledgeExtractor, ContentPreprocessor


def create_sample_whatsapp_data():
    """创建示例WhatsApp聊天数据"""
    sample_data = """
12/7/2024, 2:30 PM - 客服主管: 订单107480需要特定人员@60124088411处理，这是婴儿椅订单
12/7/2024, 2:31 PM - 客服主管: 请确保司机有婴儿椅设备
12/7/2024, 2:35 PM - 客服A: 收到，已联系司机确认设备
12/7/2024, 2:40 PM - 客服A: 司机确认有设备，订单已分配
12/7/2024, 3:15 PM - 客服主管: 订单价格需要调整，客户要求从$25调整到$30
12/7/2024, 3:16 PM - 客服主管: 调整原因：路线变更，增加了10公里
12/7/2024, 3:20 PM - 客服B: 已在系统中调整价格，客户已确认
12/7/2024, 4:00 PM - 系统管理员: 后台监控显示订单处理时间超过平均值
12/7/2024, 4:01 PM - 系统管理员: 建议优化订单分配算法
12/7/2024, 4:05 PM - 技术负责人: 已记录问题，将在下次更新中优化
"""
    
    # 保存示例数据
    sample_file = Path(__file__).parent / "sample_whatsapp_group2.txt"
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.write(sample_data)
    
    return str(sample_file)


def create_driver_sample_data():
    """创建司机问题处理示例数据"""
    driver_data = """
12/7/2024, 10:00 AM - 运营主管: 司机DENISWEECHEESIONG今天迟到30分钟
12/7/2024, 10:01 AM - 运营主管: 这是本月第3次迟到，需要按规定处理
12/7/2024, 10:05 AM - 客服主管: 已记录违规，客户投诉已收到
12/7/2024, 10:10 AM - 运营主管: 根据处罚标准，第3次迟到罚款$50
12/7/2024, 10:15 AM - 运营主管: 同时暂停服务1天，要求参加培训
12/7/2024, 10:20 AM - 人事部: 已安排明天的服务态度培训课程
12/7/2024, 11:00 AM - 运营主管: 司机60124088411无法提供婴儿椅服务
12/7/2024, 11:01 AM - 运营主管: 但接了婴儿椅订单，这是能力不匹配
12/7/2024, 11:05 AM - 客服主管: 客户很不满意，要求退款
12/7/2024, 11:10 AM - 运营主管: 已安排其他司机，给客户道歉并补偿
"""
    
    sample_file = Path(__file__).parent / "sample_whatsapp_group7.txt"
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.write(driver_data)
    
    return str(sample_file)


async def example_basic_auto_update():
    """示例1: 基本自动更新流程"""
    print("=== 示例1: 基本自动更新流程 ===")
    
    # 创建示例数据
    sample_file = create_sample_whatsapp_data()
    
    try:
        # 初始化自动更新管理器
        # 注意：需要设置GEMINI_API_KEY环境变量
        auto_manager = AutoUpdateManager()
        
        print(f"处理文件: {sample_file}")
        
        # 处理WhatsApp导出文件
        result = await auto_manager.process_whatsapp_export(sample_file, "group_2")
        
        print("处理结果:")
        print(f"  总处理: {result['total_processed']}")
        print(f"  自动批准: {result['auto_approved']}")
        print(f"  待人工审核: {result['manual_review']}")
        print(f"  已拒绝: {result['rejected']}")
        print(f"  成功率: {result['success_rate']:.2%}")
        
    except Exception as e:
        print(f"处理失败: {e}")
        print("请确保已设置GEMINI_API_KEY环境变量")
    
    finally:
        # 清理示例文件
        if os.path.exists(sample_file):
            os.remove(sample_file)
    
    print()


async def example_driver_conduct_processing():
    """示例2: 司机操守数据处理"""
    print("=== 示例2: 司机操守数据处理 ===")
    
    # 创建司机问题示例数据
    sample_file = create_driver_sample_data()
    
    try:
        auto_manager = AutoUpdateManager()
        
        print(f"处理司机问题文件: {sample_file}")
        
        # 处理司机问题数据
        result = await auto_manager.process_whatsapp_export(sample_file, "group_7")
        
        print("司机问题处理结果:")
        print(f"  总处理: {result['total_processed']}")
        print(f"  自动批准: {result['auto_approved']}")
        print(f"  待人工审核: {result['manual_review']}")
        print(f"  已拒绝: {result['rejected']}")
        
    except Exception as e:
        print(f"处理失败: {e}")
    
    finally:
        if os.path.exists(sample_file):
            os.remove(sample_file)
    
    print()


def example_message_parsing():
    """示例3: 消息解析功能"""
    print("=== 示例3: 消息解析功能 ===")
    
    # 创建示例数据
    sample_file = create_sample_whatsapp_data()
    
    try:
        # 初始化解析器
        parser = WhatsAppMessageParser()
        
        # 解析消息
        messages = parser.parse_file(sample_file)
        
        print(f"解析到 {len(messages)} 条消息:")
        
        for i, msg in enumerate(messages[:3], 1):  # 只显示前3条
            print(f"  消息 {i}:")
            print(f"    时间: {msg['timestamp']}")
            print(f"    发送者: {msg['sender']}")
            print(f"    内容: {msg['content'][:50]}...")
            print(f"    类型: {msg['message_type']}")
        
        # 获取统计信息
        stats = parser.get_message_statistics(messages)
        print(f"\n统计信息:")
        print(f"  总消息数: {stats['total_messages']}")
        print(f"  发送者数: {stats['unique_senders']}")
        print(f"  消息类型: {stats['message_types']}")
        
    except Exception as e:
        print(f"解析失败: {e}")
    
    finally:
        if os.path.exists(sample_file):
            os.remove(sample_file)
    
    print()


def example_content_preprocessing():
    """示例4: 内容预处理"""
    print("=== 示例4: 内容预处理 ===")
    
    # 模拟原始消息
    raw_messages = [
        {
            'timestamp': '2024-12-07T14:30:00',
            'sender': '客服主管',
            'content': '订单107480需要特定人员@60124088411处理   ',
            'message_type': 'text'
        },
        {
            'timestamp': '2024-12-07T14:31:00',
            'sender': '客服主管',
            'content': '这是婴儿椅订单，请确保司机有设备',
            'message_type': 'text'
        },
        {
            'timestamp': '2024-12-07T14:35:00',
            'sender': '系统',
            'content': '系统消息：用户加入了群聊',
            'message_type': 'system'
        }
    ]
    
    # 预处理配置
    filters = {
        'min_message_length': 10,
        'max_message_length': 2000,
        'exclude_patterns': ['系统消息', '加入了群聊']
    }
    
    # 初始化预处理器
    preprocessor = ContentPreprocessor()
    
    # 处理消息
    processed = preprocessor.process_messages(raw_messages, filters)
    
    print(f"原始消息: {len(raw_messages)} 条")
    print(f"处理后消息: {len(processed)} 条")
    
    for i, msg in enumerate(processed, 1):
        print(f"  处理后消息 {i}:")
        print(f"    发送者: {msg['sender']}")
        print(f"    内容: {msg['content']}")
        if 'merged_from' in msg:
            print(f"    合并自: {msg['merged_from']} 条消息")
    
    print()


async def example_review_workflow():
    """示例5: 审核工作流程"""
    print("=== 示例5: 审核工作流程 ===")
    
    try:
        auto_manager = AutoUpdateManager()
        
        # 获取待审核条目
        pending_reviews = auto_manager.get_pending_reviews()
        print(f"当前待审核条目: {len(pending_reviews)}")
        
        if pending_reviews:
            # 显示第一个待审核条目
            item = pending_reviews[0]
            print(f"示例待审核条目:")
            print(f"  ID: {item['id']}")
            print(f"  标题: {item['title']}")
            print(f"  置信度: {item.get('confidence_score', 0):.2f}")
            
            # 模拟审核决策
            print(f"\n模拟审核流程...")
            print(f"1. 人工审核条目内容")
            print(f"2. 做出审核决策")
            
            # 这里可以调用批准或拒绝方法
            # success = auto_manager.approve_review_item(item['id'])
            # 或
            # success = auto_manager.reject_review_item(item['id'], "内容不够准确")
        
        # 获取处理统计
        stats = auto_manager.get_processing_statistics()
        print(f"\n处理统计:")
        print(f"  自动批准: {stats['auto_approved']}")
        print(f"  人工批准: {stats['manually_approved']}")
        print(f"  已拒绝: {stats['rejected']}")
        print(f"  待审核: {stats['pending_review']}")
        
    except Exception as e:
        print(f"审核流程示例失败: {e}")
    
    print()


async def main():
    """主函数 - 运行所有示例"""
    print("🤖 智能化知识库自动更新系统示例")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv('GEMINI_API_KEY'):
        print("⚠️  警告: 未设置GEMINI_API_KEY环境变量")
        print("某些示例可能无法正常运行")
        print()
    
    try:
        # 运行各个示例
        example_message_parsing()
        example_content_preprocessing()
        
        # 需要API密钥的示例
        if os.getenv('GEMINI_API_KEY'):
            await example_basic_auto_update()
            await example_driver_conduct_processing()
            await example_review_workflow()
        else:
            print("跳过需要Gemini API的示例")
        
        print("✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 运行异步主函数
    asyncio.run(main())
