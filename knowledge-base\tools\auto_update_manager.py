#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能化知识库自动更新管理器
基于Google Gemini API的自动化知识提取和更新系统
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import hashlib
import re

import google.generativeai as genai
from knowledge_manager import KnowledgeManager


class AutoUpdateManager:
    """自动更新管理器主类"""
    
    def __init__(self, base_path: str = None, gemini_api_key: str = None):
        """
        初始化自动更新管理器
        
        Args:
            base_path: 知识库根目录路径
            gemini_api_key: Google Gemini API密钥
        """
        self.base_path = Path(base_path) if base_path else Path(__file__).parent.parent
        self.config_path = self.base_path / "config"
        self.auto_update_path = self.base_path / "auto_update"
        
        # 创建自动更新目录
        self.auto_update_path.mkdir(exist_ok=True)
        (self.auto_update_path / "pending").mkdir(exist_ok=True)
        (self.auto_update_path / "processed").mkdir(exist_ok=True)
        (self.auto_update_path / "rejected").mkdir(exist_ok=True)
        
        # 初始化知识库管理器
        self.kb_manager = KnowledgeManager(base_path)
        
        # 配置Gemini API
        self.gemini_api_key = gemini_api_key or os.getenv('GEMINI_API_KEY')
        if self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)
            self.model = genai.GenerativeModel('gemini-pro')
        else:
            logging.warning("Gemini API密钥未配置，将无法使用AI分析功能")
            self.model = None
        
        # 加载配置
        self.auto_update_config = self._load_auto_update_config()
        
        # 初始化组件
        self.message_parser = WhatsAppMessageParser()
        self.content_preprocessor = ContentPreprocessor()
        self.knowledge_extractor = KnowledgeExtractor(self.model, self.auto_update_config)
        self.quality_controller = QualityController(self.kb_manager.validator)
        self.review_manager = ReviewManager(self.auto_update_path)
        
        # 配置日志
        self._setup_logging()
    
    def _load_auto_update_config(self) -> Dict:
        """加载自动更新配置"""
        config_file = self.config_path / "auto_update_config.json"
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # 返回默认配置
            default_config = {
                "confidence_thresholds": {
                    "auto_approve": 0.9,
                    "manual_review": 0.7,
                    "reject": 0.5
                },
                "processing_limits": {
                    "max_messages_per_batch": 100,
                    "max_api_calls_per_hour": 1000,
                    "max_processing_time_minutes": 30
                },
                "content_filters": {
                    "min_message_length": 10,
                    "max_message_length": 2000,
                    "exclude_patterns": ["系统消息", "已删除", "撤回了一条消息"]
                },
                "group_mappings": {
                    "group_2": "general",
                    "group_7": "driver-conduct", 
                    "group_8": "general"
                }
            }
            
            # 保存默认配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            
            return default_config
    
    def _setup_logging(self):
        """配置日志系统"""
        log_file = self.auto_update_path / "auto_update.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('AutoUpdateManager')
    
    async def process_whatsapp_export(self, file_path: str, group_id: str) -> Dict[str, Any]:
        """
        处理WhatsApp导出文件
        
        Args:
            file_path: WhatsApp导出文件路径
            group_id: 群组标识符
            
        Returns:
            Dict: 处理结果统计
        """
        self.logger.info(f"开始处理WhatsApp导出文件: {file_path}")
        
        try:
            # 1. 解析消息
            messages = self.message_parser.parse_file(file_path)
            self.logger.info(f"解析到 {len(messages)} 条消息")
            
            # 2. 预处理消息
            processed_messages = self.content_preprocessor.process_messages(
                messages, self.auto_update_config["content_filters"]
            )
            self.logger.info(f"预处理后剩余 {len(processed_messages)} 条消息")
            
            # 3. 批量处理消息
            results = await self._process_messages_batch(processed_messages, group_id)
            
            # 4. 生成处理报告
            report = self._generate_processing_report(results)
            
            self.logger.info(f"处理完成: {report}")
            return report
            
        except Exception as e:
            self.logger.error(f"处理WhatsApp导出文件失败: {e}")
            raise
    
    async def _process_messages_batch(self, messages: List[Dict], group_id: str) -> List[Dict]:
        """批量处理消息"""
        results = []
        batch_size = self.auto_update_config["processing_limits"]["max_messages_per_batch"]
        
        for i in range(0, len(messages), batch_size):
            batch = messages[i:i + batch_size]
            self.logger.info(f"处理批次 {i//batch_size + 1}: {len(batch)} 条消息")
            
            # 并发处理批次中的消息
            batch_tasks = [
                self._process_single_message(msg, group_id) 
                for msg in batch
            ]
            
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # 过滤异常结果
            valid_results = [
                result for result in batch_results 
                if not isinstance(result, Exception)
            ]
            
            results.extend(valid_results)
            
            # 控制API调用频率
            await asyncio.sleep(1)
        
        return results
    
    async def _process_single_message(self, message: Dict, group_id: str) -> Optional[Dict]:
        """处理单条消息"""
        try:
            # 1. 使用Gemini API分析消息
            analysis_result = await self.knowledge_extractor.extract_knowledge(
                message, group_id
            )
            
            if not analysis_result:
                return None
            
            # 2. 质量控制
            quality_score = self.quality_controller.assess_quality(analysis_result)
            analysis_result['quality_score'] = quality_score
            
            # 3. 重复检测
            is_duplicate = self.quality_controller.check_duplicate(analysis_result)
            if is_duplicate:
                self.logger.info(f"检测到重复内容，跳过: {analysis_result.get('title', 'Unknown')}")
                return None
            
            # 4. 数据验证
            is_valid = self.quality_controller.validate_structure(analysis_result)
            if not is_valid:
                self.logger.warning(f"数据验证失败: {analysis_result.get('title', 'Unknown')}")
                return None
            
            # 5. 审核决策
            decision = self._make_review_decision(analysis_result)
            analysis_result['review_decision'] = decision
            
            # 6. 根据决策处理
            await self._handle_review_decision(analysis_result, decision)
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            return None
    
    def _make_review_decision(self, analysis_result: Dict) -> str:
        """做出审核决策"""
        confidence = analysis_result.get('confidence_score', 0)
        quality = analysis_result.get('quality_score', 0)
        
        # 综合评分
        overall_score = (confidence + quality) / 2
        
        thresholds = self.auto_update_config["confidence_thresholds"]
        
        if overall_score >= thresholds["auto_approve"]:
            return "auto_approve"
        elif overall_score >= thresholds["manual_review"]:
            return "manual_review"
        else:
            return "reject"
    
    async def _handle_review_decision(self, analysis_result: Dict, decision: str):
        """处理审核决策"""
        if decision == "auto_approve":
            # 自动添加到知识库
            success = self.kb_manager.add_knowledge_item(analysis_result)
            if success:
                self.logger.info(f"自动添加知识条目: {analysis_result['id']}")
                # 保存到已处理目录
                self._save_processed_item(analysis_result, "auto_approved")
            else:
                self.logger.error(f"自动添加失败: {analysis_result['id']}")
                
        elif decision == "manual_review":
            # 添加到人工审核队列
            self.review_manager.add_to_review_queue(analysis_result)
            self.logger.info(f"添加到人工审核队列: {analysis_result['id']}")
            
        else:  # reject
            # 保存到拒绝目录
            self._save_processed_item(analysis_result, "rejected")
            self.logger.info(f"拒绝知识条目: {analysis_result['id']}")
    
    def _save_processed_item(self, item: Dict, status: str):
        """保存已处理的条目"""
        status_dir = self.auto_update_path / status
        status_dir.mkdir(exist_ok=True)
        
        file_path = status_dir / f"{item['id']}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(item, f, ensure_ascii=False, indent=2)
    
    def _generate_processing_report(self, results: List[Dict]) -> Dict[str, Any]:
        """生成处理报告"""
        total_processed = len(results)
        auto_approved = len([r for r in results if r.get('review_decision') == 'auto_approve'])
        manual_review = len([r for r in results if r.get('review_decision') == 'manual_review'])
        rejected = len([r for r in results if r.get('review_decision') == 'reject'])
        
        return {
            "timestamp": datetime.now().isoformat(),
            "total_processed": total_processed,
            "auto_approved": auto_approved,
            "manual_review": manual_review,
            "rejected": rejected,
            "success_rate": (auto_approved + manual_review) / total_processed if total_processed > 0 else 0
        }
    
    def get_pending_reviews(self) -> List[Dict]:
        """获取待审核的条目"""
        return self.review_manager.get_pending_reviews()
    
    def approve_review_item(self, item_id: str) -> bool:
        """批准审核条目"""
        item = self.review_manager.get_review_item(item_id)
        if not item:
            return False
        
        # 添加到知识库
        success = self.kb_manager.add_knowledge_item(item)
        if success:
            # 从审核队列移除
            self.review_manager.remove_from_queue(item_id)
            # 保存到已处理目录
            self._save_processed_item(item, "manually_approved")
            self.logger.info(f"人工批准知识条目: {item_id}")
            return True
        
        return False
    
    def reject_review_item(self, item_id: str, reason: str = "") -> bool:
        """拒绝审核条目"""
        item = self.review_manager.get_review_item(item_id)
        if not item:
            return False
        
        # 添加拒绝原因
        item['rejection_reason'] = reason
        item['rejected_at'] = datetime.now().isoformat()
        
        # 从审核队列移除
        self.review_manager.remove_from_queue(item_id)
        # 保存到拒绝目录
        self._save_processed_item(item, "manually_rejected")
        
        self.logger.info(f"人工拒绝知识条目: {item_id}, 原因: {reason}")
        return True
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = {
            "auto_approved": len(list((self.auto_update_path / "auto_approved").glob("*.json"))),
            "manually_approved": len(list((self.auto_update_path / "manually_approved").glob("*.json"))),
            "rejected": len(list((self.auto_update_path / "rejected").glob("*.json"))),
            "manually_rejected": len(list((self.auto_update_path / "manually_rejected").glob("*.json"))),
            "pending_review": len(self.get_pending_reviews())
        }
        
        stats["total_processed"] = sum([
            stats["auto_approved"],
            stats["manually_approved"], 
            stats["rejected"],
            stats["manually_rejected"]
        ])
        
        return stats


# 辅助类定义将在后续文件中实现
class WhatsAppMessageParser:
    """WhatsApp消息解析器"""
    
    def parse_file(self, file_path: str) -> List[Dict]:
        """解析WhatsApp导出文件"""
        # 实现消息解析逻辑
        pass


class ContentPreprocessor:
    """内容预处理器"""
    
    def process_messages(self, messages: List[Dict], filters: Dict) -> List[Dict]:
        """预处理消息"""
        # 实现消息预处理逻辑
        pass


class KnowledgeExtractor:
    """知识提取器"""
    
    def __init__(self, model, config):
        self.model = model
        self.config = config
    
    async def extract_knowledge(self, message: Dict, group_id: str) -> Optional[Dict]:
        """从消息中提取知识"""
        # 实现知识提取逻辑
        pass


class QualityController:
    """质量控制器"""
    
    def __init__(self, validator):
        self.validator = validator
    
    def assess_quality(self, item: Dict) -> float:
        """评估质量分数"""
        # 实现质量评估逻辑
        pass
    
    def check_duplicate(self, item: Dict) -> bool:
        """检查重复"""
        # 实现重复检测逻辑
        pass
    
    def validate_structure(self, item: Dict) -> bool:
        """验证数据结构"""
        return self.validator.validate_item(item)


class ReviewManager:
    """审核管理器"""
    
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.pending_path = base_path / "pending"
    
    def add_to_review_queue(self, item: Dict):
        """添加到审核队列"""
        file_path = self.pending_path / f"{item['id']}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(item, f, ensure_ascii=False, indent=2)
    
    def get_pending_reviews(self) -> List[Dict]:
        """获取待审核条目"""
        items = []
        for file_path in self.pending_path.glob("*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    item = json.load(f)
                    items.append(item)
            except Exception as e:
                logging.error(f"读取审核条目失败 {file_path}: {e}")
        return items
    
    def get_review_item(self, item_id: str) -> Optional[Dict]:
        """获取特定审核条目"""
        file_path = self.pending_path / f"{item_id}.json"
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return None
    
    def remove_from_queue(self, item_id: str) -> bool:
        """从审核队列移除"""
        file_path = self.pending_path / f"{item_id}.json"
        try:
            file_path.unlink()
            return True
        except FileNotFoundError:
            return False
