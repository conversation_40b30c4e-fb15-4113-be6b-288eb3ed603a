# 知识库系统用户使用指南

## 📖 概述

本指南将帮助您快速上手知识库系统，了解如何搜索、浏览和管理知识条目。

## 🚀 快速开始

### 1. 系统初始化

首次使用前，需要初始化知识库目录结构：

```bash
cd knowledge-base/tools
python knowledge_manager.py init
```

### 2. 启动Web界面

推荐使用Web界面进行日常操作：

```bash
python web_interface.py
```

然后在浏览器中访问 `http://localhost:5000`

### 3. 命令行工具

也可以使用命令行工具进行操作：

```bash
# 搜索知识
python knowledge_manager.py search --query "订单处理"

# 列出所有条目
python knowledge_manager.py list

# 获取特定条目
python knowledge_manager.py get --id "order_processing_workflow_001"
```

## 🔍 搜索功能

### Web界面搜索

1. **基本搜索**：在搜索框中输入关键词
2. **分类过滤**：选择特定分类进行过滤
3. **标签过滤**：输入标签名称（逗号分隔）
4. **可信度过滤**：按可信度等级筛选

### 命令行搜索

```bash
# 基本搜索
python knowledge_manager.py search --query "价格调整"

# 带分类过滤的搜索
python knowledge_manager.py search --query "司机" --category "violations"

# 带标签过滤的搜索
python knowledge_manager.py search --query "订单" --tags "urgent,order_processing"
```

### 搜索技巧

- **关键词选择**：使用具体的业务术语效果更好
- **组合过滤**：结合分类和标签过滤可以更精确地定位内容
- **模糊搜索**：系统支持模糊匹配，输入部分关键词也能找到相关内容

## 📚 浏览知识库

### 按分类浏览

知识库分为两大模块：

#### 通用知识库
- **技术问题** (`technical`)：系统技术问题和解决方案
- **流程规范** (`process`)：业务流程和操作规范  
- **常见问题** (`faq`)：频繁出现的问题和标准答案

#### 司机操守知识库
- **行为规范** (`regulations`)：司机应遵守的行为准则
- **违规案例** (`violations`)：司机违规行为的记录和分析
- **处罚标准** (`penalties`)：不同违规行为对应的处罚措施

### 标签系统

使用标签可以快速定位相关内容：

#### 通用标签
- `order_processing`：订单处理相关
- `customer_service`：客服服务相关
- `urgent`：紧急事项
- `important`：重要事项
- `third_party`：第三方平台相关

#### 司机操守标签
- `tardiness`：迟到相关
- `no_show`：爽约相关
- `service_attitude`：服务态度相关
- `financial_penalty`：经济处罚相关
- `service_suspension`：暂停服务相关

## ➕ 添加知识条目

### 使用Web界面添加

1. 点击"➕ 添加条目"标签
2. 填写必需字段：
   - **条目ID**：唯一标识符（建议格式：`category_topic_001`）
   - **标题**：简洁明确的标题
   - **描述**：详细描述条目内容
   - **分类**：选择合适的分类
   - **标签**：添加相关标签（逗号分隔）
   - **内容摘要**：简要总结
   - **详细内容**：完整的内容描述
3. 点击"添加条目"按钮

### 使用命令行添加

1. 创建JSON文件（可参考模板）：

```json
{
  "id": "new_knowledge_item_001",
  "title": "新知识条目标题",
  "description": "详细描述",
  "category": "process",
  "tags": ["example", "new_item"],
  "content": {
    "summary": "简要总结",
    "details": "详细内容"
  },
  "metadata": {
    "created_at": "2025-07-09T12:00:00Z",
    "updated_at": "2025-07-09T12:00:00Z",
    "version": "1.0.0",
    "author": "用户名"
  }
}
```

2. 使用命令行添加：

```bash
python knowledge_manager.py add --file new_item.json
```

## 🔧 管理功能

### 验证数据完整性

定期验证知识库数据的完整性：

```bash
python knowledge_manager.py validate
```

### 查看统计信息

了解知识库的整体情况：

- **Web界面**：点击"📊 统计信息"标签
- **命令行**：使用搜索引擎的统计功能

### 更新条目

目前支持通过删除旧条目并添加新条目的方式进行更新。

## 💡 最佳实践

### 1. 条目命名规范

- **ID格式**：`category_topic_sequence`
- **示例**：`order_processing_workflow_001`、`driver_tardiness_violation_001`

### 2. 标签使用建议

- 使用小写字母和下划线
- 每个条目建议3-8个标签
- 包含业务类型、紧急程度、涉及部门等维度

### 3. 内容编写规范

- **标题**：简洁明确，突出核心内容
- **描述**：详细说明适用场景和解决的问题
- **摘要**：一句话概括核心要点
- **详细内容**：结构化描述，包含具体步骤或案例

### 4. 关联关系维护

- 及时更新相关条目的关联关系
- 建立前置条件和后续步骤的链接
- 标记被替代的旧条目

## ❓ 常见问题

### Q: 如何找到特定业务场景的解决方案？

A: 建议使用组合搜索：先选择相关分类，然后输入具体的业务关键词，再结合标签过滤。

### Q: 添加的条目没有显示在搜索结果中？

A: 检查以下几点：
1. 条目ID是否唯一
2. 必需字段是否完整
3. 数据格式是否正确
4. 可以使用验证功能检查数据完整性

### Q: 如何批量导入知识条目？

A: 目前支持单个条目添加，批量导入功能可以通过编写脚本调用管理工具的API实现。

### Q: 搜索结果太多，如何精确定位？

A: 使用多重过滤条件：
1. 选择具体的分类
2. 添加相关标签
3. 使用更具体的关键词
4. 利用可信度过滤

## 📞 技术支持

如果在使用过程中遇到问题，可以：

1. 查看系统日志文件
2. 使用验证工具检查数据完整性
3. 参考维护手册进行故障排除
4. 联系系统管理员

---

*最后更新：2025-07-09*  
*版本：1.0.0*
