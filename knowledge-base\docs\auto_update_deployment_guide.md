# 智能化知识库自动更新系统部署指南

## 🚀 快速开始

### 1. 环境准备

#### 系统要求
- Python 3.8+
- 至少4GB可用内存
- 稳定的网络连接（用于Gemini API调用）

#### 依赖安装
```bash
# 安装基础依赖
pip install google-generativeai flask pyyaml jsonschema pathlib asyncio

# 或使用requirements.txt
pip install -r requirements.txt
```

#### API密钥配置
```bash
# 设置Google Gemini API密钥
export GEMINI_API_KEY="your_gemini_api_key_here"

# Windows用户
set GEMINI_API_KEY=your_gemini_api_key_here
```

### 2. 系统初始化

```bash
# 进入知识库目录
cd knowledge-base

# 初始化知识库结构
python tools/knowledge_manager.py init

# 创建自动更新目录
mkdir -p auto_update/{pending,processed,rejected}
mkdir -p uploads
```

### 3. 启动系统

#### 启动Web界面
```bash
python tools/web_interface.py
```

#### 命令行使用
```bash
# 处理WhatsApp导出文件
python -c "
import asyncio
from tools.auto_update_manager import AutoUpdateManager

async def process_file():
    manager = AutoUpdateManager()
    result = await manager.process_whatsapp_export('path/to/whatsapp_export.txt', 'group_2')
    print(result)

asyncio.run(process_file())
"
```

## 📋 配置说明

### 自动更新配置 (config/auto_update_config.json)

```json
{
  "confidence_thresholds": {
    "auto_approve": 0.9,      // 自动批准阈值
    "manual_review": 0.7,     // 人工审核阈值
    "reject": 0.5             // 拒绝阈值
  },
  "processing_limits": {
    "max_messages_per_batch": 100,        // 每批次最大消息数
    "max_api_calls_per_hour": 1000,       // 每小时最大API调用数
    "max_processing_time_minutes": 30     // 最大处理时间
  },
  "group_mappings": {
    "group_2": {
      "name": "客服团队",
      "knowledge_type": "general",
      "default_category": "process"
    }
  }
}
```

### 关键配置项说明

#### 置信度阈值
- **auto_approve (0.9)**: 置信度≥90%的内容自动添加到知识库
- **manual_review (0.7)**: 置信度70%-90%的内容进入人工审核队列
- **reject (0.5)**: 置信度<50%的内容直接拒绝

#### 处理限制
- **max_messages_per_batch**: 控制批处理大小，避免内存溢出
- **max_api_calls_per_hour**: 控制API调用频率，避免超出配额
- **max_processing_time_minutes**: 设置处理超时时间

## 🔧 使用方法

### Web界面使用

1. **访问系统**: 打开浏览器访问 `http://localhost:5000`

2. **智能更新**:
   - 点击"🤖 智能更新"标签
   - 选择WhatsApp导出文件
   - 选择对应的群组
   - 点击"开始智能分析"

3. **审核管理**:
   - 点击"👁️ 审核管理"标签
   - 查看待审核条目
   - 对条目进行批准或拒绝操作

### 命令行使用

#### 处理单个文件
```python
import asyncio
from tools.auto_update_manager import AutoUpdateManager

async def process_whatsapp_file():
    manager = AutoUpdateManager()
    
    # 处理文件
    result = await manager.process_whatsapp_export(
        file_path="exports/group2_chat.txt",
        group_id="group_2"
    )
    
    print(f"处理结果: {result}")

# 运行
asyncio.run(process_whatsapp_file())
```

#### 批量处理
```python
import asyncio
import os
from tools.auto_update_manager import AutoUpdateManager

async def batch_process():
    manager = AutoUpdateManager()
    
    # 处理目录中的所有文件
    export_dir = "exports/"
    for filename in os.listdir(export_dir):
        if filename.endswith('.txt'):
            file_path = os.path.join(export_dir, filename)
            
            # 根据文件名确定群组
            if 'group2' in filename:
                group_id = 'group_2'
            elif 'group7' in filename:
                group_id = 'group_7'
            else:
                group_id = 'group_8'
            
            result = await manager.process_whatsapp_export(file_path, group_id)
            print(f"处理 {filename}: {result}")

asyncio.run(batch_process())
```

### 审核工作流

#### 获取待审核条目
```python
from tools.auto_update_manager import AutoUpdateManager

manager = AutoUpdateManager()

# 获取待审核列表
pending_items = manager.get_pending_reviews()
print(f"待审核条目: {len(pending_items)}")

for item in pending_items:
    print(f"- {item['id']}: {item['title']}")
```

#### 批准条目
```python
# 批准特定条目
success = manager.approve_review_item("auto_extracted_001")
if success:
    print("批准成功")
```

#### 拒绝条目
```python
# 拒绝条目并说明原因
success = manager.reject_review_item("auto_extracted_002", "内容不够准确")
if success:
    print("拒绝成功")
```

## 📊 监控和维护

### 系统监控

#### 处理统计
```python
from tools.auto_update_manager import AutoUpdateManager

manager = AutoUpdateManager()
stats = manager.get_processing_statistics()

print(f"自动批准: {stats['auto_approved']}")
print(f"人工批准: {stats['manually_approved']}")
print(f"已拒绝: {stats['rejected']}")
print(f"待审核: {stats['pending_review']}")
```

#### API使用统计
```python
from tools.gemini_integration import GeminiKnowledgeExtractor

# 如果有API密钥
if os.getenv('GEMINI_API_KEY'):
    extractor = GeminiKnowledgeExtractor(os.getenv('GEMINI_API_KEY'), {})
    usage = extractor.get_api_usage_stats()
    print(f"本小时API调用: {usage['calls_this_hour']}")
```

### 日志管理

#### 查看处理日志
```bash
# 查看自动更新日志
tail -f auto_update/auto_update.log

# 查看Web界面日志
tail -f knowledge_base.log
```

#### 日志级别配置
```python
import logging

# 设置详细日志
logging.getLogger('AutoUpdateManager').setLevel(logging.DEBUG)
logging.getLogger('GeminiKnowledgeExtractor').setLevel(logging.DEBUG)
```

### 数据备份

#### 自动备份
系统会自动备份处理结果到以下目录：
- `auto_update/processed/` - 已处理的条目
- `auto_update/rejected/` - 已拒绝的条目
- `auto_update/pending/` - 待审核的条目

#### 手动备份
```bash
# 备份整个知识库
tar -czf knowledge_base_backup_$(date +%Y%m%d).tar.gz knowledge-base/

# 只备份数据
tar -czf data_backup_$(date +%Y%m%d).tar.gz knowledge-base/data/
```

## 🔍 故障排除

### 常见问题

#### 1. Gemini API调用失败
```
错误: API调用失败
解决: 检查API密钥是否正确设置，网络连接是否正常
```

#### 2. 文件解析失败
```
错误: 无法解析WhatsApp导出文件
解决: 检查文件格式，确保是标准的WhatsApp导出格式
```

#### 3. 内存不足
```
错误: 处理大文件时内存不足
解决: 减少max_messages_per_batch配置值
```

#### 4. 处理超时
```
错误: 处理时间过长
解决: 增加max_processing_time_minutes配置值
```

### 调试模式

#### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 测试API连接
```python
import os
import google.generativeai as genai

# 测试Gemini API
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))
model = genai.GenerativeModel('gemini-pro')

try:
    response = model.generate_content("测试连接")
    print("API连接正常")
except Exception as e:
    print(f"API连接失败: {e}")
```

## 📈 性能优化

### 提升处理速度

1. **调整批处理大小**
```json
{
  "processing_limits": {
    "max_messages_per_batch": 50  // 减少批处理大小
  }
}
```

2. **启用并发处理**
```python
# 在auto_update_manager.py中调整并发数
semaphore = asyncio.Semaphore(5)  // 最多5个并发任务
```

3. **优化API调用**
```json
{
  "gemini_config": {
    "temperature": 0.1,      // 降低随机性
    "max_output_tokens": 1024 // 减少输出长度
  }
}
```

### 降低成本

1. **设置API调用限制**
```json
{
  "processing_limits": {
    "max_api_calls_per_hour": 500  // 降低每小时调用数
  }
}
```

2. **提高过滤精度**
```json
{
  "content_filters": {
    "min_message_length": 20,  // 提高最小长度要求
    "exclude_patterns": [      // 增加排除模式
      "系统消息", "已删除", "表情符号"
    ]
  }
}
```

## 🔐 安全配置

### API密钥安全
```bash
# 使用环境变量
export GEMINI_API_KEY="your_key"

# 或使用配置文件（不要提交到版本控制）
echo "GEMINI_API_KEY=your_key" > .env
```

### 数据脱敏
系统自动脱敏以下信息：
- 电话号码 → [电话号码]
- 邮箱地址 → [邮箱地址]
- 身份证号 → [身份证号]

### 访问控制
```python
# 在web_interface.py中添加认证
from functools import wraps

def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 添加认证逻辑
        return f(*args, **kwargs)
    return decorated_function

@app.route('/api/auto-update/upload', methods=['POST'])
@require_auth
def api_upload_whatsapp_file():
    # 受保护的端点
    pass
```

---

*部署指南版本：1.0.0*  
*最后更新：2025-07-09*
