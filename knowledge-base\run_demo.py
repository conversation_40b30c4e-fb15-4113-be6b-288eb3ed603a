#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库系统快速启动脚本
用于快速演示和测试重构后的脚本架构
"""

import os
import sys
import webbrowser
from pathlib import Path


def main():
    """主函数"""
    print("🚀 知识库系统 - 传统脚本架构")
    print("=" * 50)
    
    base_path = Path(__file__).parent
    
    print("选择操作:")
    print("1. 运行演示脚本 (创建示例数据并处理)")
    print("2. 查看可视化仪表盘 (需要先运行演示)")
    print("3. 查看项目结构")
    print("4. 检查环境依赖")
    print("5. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-5): ").strip()
            
            if choice == '1':
                run_demo(base_path)
            elif choice == '2':
                open_dashboard(base_path)
            elif choice == '3':
                show_project_structure(base_path)
            elif choice == '4':
                check_dependencies()
            elif choice == '5':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


def run_demo(base_path: Path):
    """运行演示脚本"""
    print("\n🎬 运行演示脚本...")
    
    demo_script = base_path / "examples" / "script_demo.py"
    
    if not demo_script.exists():
        print(f"❌ 演示脚本不存在: {demo_script}")
        return
    
    try:
        # 运行演示脚本
        import subprocess
        result = subprocess.run([
            sys.executable, str(demo_script)
        ], cwd=str(base_path), capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 演示脚本运行成功！")
            print("\n📄 输出:")
            print(result.stdout)
        else:
            print("❌ 演示脚本运行失败！")
            print(f"错误: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 运行演示脚本时发生错误: {e}")


def open_dashboard(base_path: Path):
    """打开可视化仪表盘"""
    print("\n🌐 打开可视化仪表盘...")
    
    dashboard_path = base_path / "static" / "dashboard.html"
    
    if not dashboard_path.exists():
        print(f"❌ 仪表盘文件不存在: {dashboard_path}")
        print("请先运行演示脚本生成可视化文件")
        return
    
    try:
        # 在浏览器中打开仪表盘
        dashboard_url = f"file://{dashboard_path.absolute()}"
        webbrowser.open(dashboard_url)
        print(f"✅ 已在浏览器中打开仪表盘")
        print(f"📍 路径: {dashboard_url}")
        
        # 检查其他可视化页面
        other_pages = [
            ("知识网络", "knowledge_network.html"),
            ("趋势分析", "trend_analysis.html")
        ]
        
        print("\n📊 其他可视化页面:")
        for name, filename in other_pages:
            page_path = base_path / "static" / filename
            if page_path.exists():
                page_url = f"file://{page_path.absolute()}"
                print(f"  🔗 {name}: {page_url}")
            else:
                print(f"  ❌ {name}: 文件不存在")
                
    except Exception as e:
        print(f"❌ 打开仪表盘时发生错误: {e}")


def show_project_structure(base_path: Path):
    """显示项目结构"""
    print("\n📁 项目结构:")
    
    structure = """
knowledge-base/
├── scripts/                         # 🔧 核心脚本模块
│   ├── main_processor.py            #   主执行脚本
│   ├── conversation_analyzer.py     #   对话分析器
│   ├── entity_extractor.py          #   实体提取器
│   ├── visualization_generator.py   #   可视化数据生成器
│   └── network_builder.py           #   知识网络构建器
├── static/                          # 🌐 静态可视化文件
│   ├── dashboard.html               #   主仪表盘
│   ├── knowledge_network.html       #   知识网络可视化
│   ├── trend_analysis.html          #   趋势分析页面
│   ├── css/dashboard.css            #   样式文件
│   ├── js/dashboard.js              #   JavaScript脚本
│   └── data/                        #   生成的JSON数据
├── output/                          # 📤 处理结果输出
│   ├── knowledge_base.json          #   结构化知识库
│   ├── processing_report.json       #   处理报告
│   └── visualization_data/          #   可视化数据
├── config/                          # ⚙️ 配置文件
│   └── script_config.json           #   脚本配置
├── examples/                        # 📚 示例和演示
│   └── script_demo.py               #   使用演示
├── tools/                           # 🛠️ 复用的工具模块
│   ├── knowledge_manager.py         #   知识库管理器
│   ├── whatsapp_parser.py           #   WhatsApp解析器
│   └── gemini_integration.py        #   Gemini API集成
└── README_SCRIPT_ARCHITECTURE.md    # 📖 架构说明文档
    """
    
    print(structure)
    
    # 检查关键文件是否存在
    print("🔍 关键文件检查:")
    key_files = [
        ("scripts/main_processor.py", "主执行脚本"),
        ("config/script_config.json", "脚本配置"),
        ("examples/script_demo.py", "演示脚本"),
        ("README_SCRIPT_ARCHITECTURE.md", "架构文档")
    ]
    
    for file_path, description in key_files:
        full_path = base_path / file_path
        status = "✅" if full_path.exists() else "❌"
        print(f"  {status} {description}: {file_path}")


def check_dependencies():
    """检查环境依赖"""
    print("\n🔍 检查环境依赖...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version >= (3, 8):
        print("  ✅ Python版本满足要求 (>= 3.8)")
    else:
        print("  ❌ Python版本过低，需要 >= 3.8")
    
    # 检查必需的包
    required_packages = [
        'json',
        'pathlib', 
        'datetime',
        'logging',
        'asyncio',
        'collections',
        'typing'
    ]
    
    print("\n📦 内置模块检查:")
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - 未找到")
    
    # 检查可选的包
    optional_packages = [
        ('google.generativeai', 'Gemini API支持'),
        ('flask', 'Web服务器支持（已移除）'),
        ('pyyaml', 'YAML配置支持'),
        ('jsonschema', 'JSON验证支持')
    ]
    
    print("\n📦 可选模块检查:")
    for package, description in optional_packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ⚠️ {package} - {description} (可选)")
    
    # 检查环境变量
    print("\n🔑 环境变量检查:")
    gemini_key = os.getenv('GEMINI_API_KEY')
    if gemini_key:
        print(f"  ✅ GEMINI_API_KEY - 已设置 (长度: {len(gemini_key)})")
    else:
        print("  ⚠️ GEMINI_API_KEY - 未设置 (AI功能将不可用)")
    
    # 检查磁盘空间
    print("\n💾 系统资源检查:")
    try:
        import shutil
        total, used, free = shutil.disk_usage(Path.cwd())
        free_gb = free // (1024**3)
        print(f"  💿 可用磁盘空间: {free_gb} GB")
        
        if free_gb >= 1:
            print("  ✅ 磁盘空间充足")
        else:
            print("  ⚠️ 磁盘空间不足，建议至少1GB")
            
    except Exception as e:
        print(f"  ❌ 无法检查磁盘空间: {e}")


if __name__ == '__main__':
    main()
