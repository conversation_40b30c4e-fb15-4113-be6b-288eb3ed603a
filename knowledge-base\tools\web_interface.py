#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库Web界面
提供基于Web的知识库管理和查询界面
"""

from flask import Flask, render_template_string, request, jsonify, redirect, url_for
import json
from pathlib import Path
from knowledge_manager import KnowledgeManager


app = Flask(__name__)
app.secret_key = 'knowledge_base_secret_key'

# 初始化知识库管理器
manager = KnowledgeManager()

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; color: #333; }
        .nav { display: flex; gap: 10px; margin-bottom: 20px; }
        .nav button { padding: 10px 20px; border: none; background: #007bff; color: white; border-radius: 4px; cursor: pointer; }
        .nav button:hover { background: #0056b3; }
        .nav button.active { background: #28a745; }
        .section { display: none; }
        .section.active { display: block; }
        .search-box { width: 100%; padding: 10px; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 4px; }
        .filters { display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap; }
        .filters select, .filters input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .item-list { display: grid; gap: 15px; }
        .item-card { border: 1px solid #ddd; padding: 15px; border-radius: 4px; background: #f9f9f9; }
        .item-title { font-weight: bold; color: #007bff; margin-bottom: 5px; }
        .item-meta { font-size: 12px; color: #666; margin-bottom: 10px; }
        .item-tags { margin-top: 10px; }
        .tag { display: inline-block; background: #e9ecef; padding: 2px 8px; margin: 2px; border-radius: 12px; font-size: 11px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .form-group textarea { height: 100px; resize: vertical; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 4px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 知识库管理系统</h1>
            <p>WhatsApp业务知识库 - 可持续维护的结构化知识管理</p>
        </div>
        
        <div class="nav">
            <button onclick="showSection('search')" class="active">🔍 搜索知识</button>
            <button onclick="showSection('browse')">📖 浏览分类</button>
            <button onclick="showSection('add')">➕ 添加条目</button>
            <button onclick="showSection('auto-update')">🤖 智能更新</button>
            <button onclick="showSection('review')">👁️ 审核管理</button>
            <button onclick="showSection('stats')">📊 统计信息</button>
        </div>
        
        <!-- 搜索部分 -->
        <div id="search" class="section active">
            <h2>搜索知识库</h2>
            <input type="text" id="searchQuery" class="search-box" placeholder="输入搜索关键词..." onkeyup="performSearch()">
            
            <div class="filters">
                <select id="categoryFilter" onchange="performSearch()">
                    <option value="">所有分类</option>
                    <option value="technical">技术问题</option>
                    <option value="process">流程规范</option>
                    <option value="faq">常见问题</option>
                    <option value="violations">违规案例</option>
                    <option value="regulations">行为规范</option>
                    <option value="penalties">处罚标准</option>
                </select>
                
                <input type="text" id="tagsFilter" placeholder="标签过滤（逗号分隔）" onkeyup="performSearch()">
                
                <select id="confidenceFilter" onchange="performSearch()">
                    <option value="">所有可信度</option>
                    <option value="high">高</option>
                    <option value="medium">中</option>
                    <option value="low">低</option>
                </select>
            </div>
            
            <div id="searchResults" class="item-list"></div>
        </div>
        
        <!-- 浏览部分 -->
        <div id="browse" class="section">
            <h2>按分类浏览</h2>
            <div id="browseResults" class="item-list"></div>
        </div>
        
        <!-- 添加部分 -->
        <div id="add" class="section">
            <h2>添加知识条目</h2>
            <form id="addForm">
                <div class="form-group">
                    <label>条目ID *</label>
                    <input type="text" id="itemId" required>
                </div>
                
                <div class="form-group">
                    <label>标题 *</label>
                    <input type="text" id="itemTitle" required>
                </div>
                
                <div class="form-group">
                    <label>描述 *</label>
                    <textarea id="itemDescription" required></textarea>
                </div>
                
                <div class="form-group">
                    <label>分类 *</label>
                    <select id="itemCategory" required>
                        <option value="">请选择分类</option>
                        <option value="technical">技术问题</option>
                        <option value="process">流程规范</option>
                        <option value="faq">常见问题</option>
                        <option value="violations">违规案例</option>
                        <option value="regulations">行为规范</option>
                        <option value="penalties">处罚标准</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>标签（逗号分隔）</label>
                    <input type="text" id="itemTags" placeholder="例如: order_processing, urgent, customer_service">
                </div>
                
                <div class="form-group">
                    <label>内容摘要 *</label>
                    <textarea id="itemSummary" required></textarea>
                </div>
                
                <div class="form-group">
                    <label>详细内容 *</label>
                    <textarea id="itemDetails" required style="height: 150px;"></textarea>
                </div>
                
                <button type="submit" class="btn btn-success">添加条目</button>
                <button type="button" class="btn btn-primary" onclick="loadTemplate()">加载模板</button>
            </form>
        </div>
        
        <!-- 智能更新部分 -->
        <div id="auto-update" class="section">
            <h2>智能知识库更新</h2>

            <div class="form-group">
                <label>上传WhatsApp导出文件</label>
                <input type="file" id="whatsappFile" accept=".txt,.json,.csv">

                <label>选择群组</label>
                <select id="groupSelect">
                    <option value="group_2">群组2 - 客服团队</option>
                    <option value="group_7">群组7 - 司机问题处理</option>
                    <option value="group_8">群组8 - 各部门信息交流</option>
                </select>

                <button type="button" class="btn btn-primary" onclick="uploadWhatsAppFile()">开始智能分析</button>
            </div>

            <div id="uploadProgress" style="display: none;">
                <h3>处理进度</h3>
                <div class="progress-bar">
                    <div id="progressFill" style="width: 0%; background: #007bff; height: 20px; border-radius: 10px;"></div>
                </div>
                <p id="progressText">准备处理...</p>
            </div>

            <div id="uploadResults" style="display: none;">
                <h3>处理结果</h3>
                <div id="resultsContent"></div>
            </div>
        </div>

        <!-- 审核管理部分 -->
        <div id="review" class="section">
            <h2>审核管理</h2>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="pendingCount">0</div>
                    <div class="stat-label">待审核条目</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="approvedCount">0</div>
                    <div class="stat-label">已批准条目</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="rejectedCount">0</div>
                    <div class="stat-label">已拒绝条目</div>
                </div>
            </div>

            <h3>待审核条目</h3>
            <div id="pendingReviews" class="item-list"></div>
        </div>

        <!-- 统计部分 -->
        <div id="stats" class="section">
            <h2>知识库统计</h2>
            <div id="statsContent"></div>
        </div>
    </div>
    
    <script>
        function showSection(sectionId) {
            // 隐藏所有部分
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 移除所有按钮的active类
            document.querySelectorAll('.nav button').forEach(button => {
                button.classList.remove('active');
            });
            
            // 显示选中的部分
            document.getElementById(sectionId).classList.add('active');
            event.target.classList.add('active');
            
            // 加载相应内容
            if (sectionId === 'browse') {
                loadBrowseContent();
            } else if (sectionId === 'stats') {
                loadStats();
            } else if (sectionId === 'review') {
                loadReviewContent();
            }
        }
        
        function performSearch() {
            const query = document.getElementById('searchQuery').value;
            const category = document.getElementById('categoryFilter').value;
            const tags = document.getElementById('tagsFilter').value;
            const confidence = document.getElementById('confidenceFilter').value;
            
            const params = new URLSearchParams();
            if (query) params.append('query', query);
            if (category) params.append('category', category);
            if (tags) params.append('tags', tags);
            if (confidence) params.append('confidence_level', confidence);
            
            fetch(`/api/search?${params}`)
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data);
                })
                .catch(error => {
                    console.error('搜索失败:', error);
                });
        }
        
        function displaySearchResults(results) {
            const container = document.getElementById('searchResults');
            
            if (results.length === 0) {
                container.innerHTML = '<p>未找到相关结果</p>';
                return;
            }
            
            container.innerHTML = results.map(item => `
                <div class="item-card">
                    <div class="item-title">${item.title}</div>
                    <div class="item-meta">
                        ID: ${item.id} | 分类: ${item.category} | 
                        更新时间: ${item.metadata?.updated_at || '未知'}
                    </div>
                    <div>${item.description}</div>
                    <div class="item-tags">
                        ${(item.tags || []).map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                </div>
            `).join('');
        }
        
        function loadBrowseContent() {
            fetch('/api/list')
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data);
                    document.getElementById('browseResults').innerHTML = 
                        document.getElementById('searchResults').innerHTML;
                });
        }
        
        function loadStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    displayStats(data);
                });
        }
        
        function displayStats(stats) {
            const container = document.getElementById('statsContent');
            
            let html = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">${stats.total_items}</div>
                        <div class="stat-label">总条目数</div>
                    </div>
                </div>
                
                <h3>分类统计</h3>
                <div class="stats-grid">
            `;
            
            for (const [category, count] of Object.entries(stats.categories)) {
                html += `
                    <div class="stat-card">
                        <div class="stat-number">${count}</div>
                        <div class="stat-label">${category}</div>
                    </div>
                `;
            }
            
            html += '</div>';
            
            container.innerHTML = html;
        }
        
        // 智能更新功能
        function uploadWhatsAppFile() {
            const fileInput = document.getElementById('whatsappFile');
            const groupSelect = document.getElementById('groupSelect');

            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('group_id', groupSelect.value);

            // 显示进度
            document.getElementById('uploadProgress').style.display = 'block';
            document.getElementById('uploadResults').style.display = 'none';

            fetch('/api/auto-update/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('uploadProgress').style.display = 'none';
                document.getElementById('uploadResults').style.display = 'block';

                if (data.success) {
                    document.getElementById('resultsContent').innerHTML = `
                        <div class="stat-card">
                            <h4>处理完成</h4>
                            <p>总处理: ${data.result.total_processed}</p>
                            <p>自动批准: ${data.result.auto_approved}</p>
                            <p>待人工审核: ${data.result.manual_review}</p>
                            <p>已拒绝: ${data.result.rejected}</p>
                        </div>
                    `;
                } else {
                    document.getElementById('resultsContent').innerHTML = `
                        <div style="color: red;">处理失败: ${data.error}</div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('uploadProgress').style.display = 'none';
                document.getElementById('uploadResults').style.display = 'block';
                document.getElementById('resultsContent').innerHTML = `
                    <div style="color: red;">上传失败: ${error}</div>
                `;
            });
        }

        // 审核管理功能
        function loadReviewContent() {
            // 加载统计信息
            fetch('/api/auto-update/statistics')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('pendingCount').textContent = data.pending_review || 0;
                    document.getElementById('approvedCount').textContent = (data.auto_approved || 0) + (data.manually_approved || 0);
                    document.getElementById('rejectedCount').textContent = (data.rejected || 0) + (data.manually_rejected || 0);
                });

            // 加载待审核条目
            fetch('/api/auto-update/pending-reviews')
                .then(response => response.json())
                .then(data => {
                    displayPendingReviews(data);
                });
        }

        function displayPendingReviews(reviews) {
            const container = document.getElementById('pendingReviews');

            if (reviews.length === 0) {
                container.innerHTML = '<p>暂无待审核条目</p>';
                return;
            }

            container.innerHTML = reviews.map(item => `
                <div class="item-card">
                    <div class="item-title">${item.title}</div>
                    <div class="item-meta">
                        ID: ${item.id} | 分类: ${item.category} |
                        置信度: ${(item.confidence_score * 100).toFixed(1)}%
                    </div>
                    <div>${item.description}</div>
                    <div class="item-tags">
                        ${(item.tags || []).map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-success" onclick="approveReview('${item.id}')">批准</button>
                        <button class="btn btn-danger" onclick="rejectReview('${item.id}')">拒绝</button>
                    </div>
                </div>
            `).join('');
        }

        function approveReview(itemId) {
            if (!confirm('确定要批准这个条目吗？')) return;

            fetch(`/api/auto-update/approve/${itemId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('批准成功');
                    loadReviewContent(); // 重新加载
                } else {
                    alert('批准失败: ' + data.error);
                }
            });
        }

        function rejectReview(itemId) {
            const reason = prompt('请输入拒绝原因:');
            if (!reason) return;

            fetch(`/api/auto-update/reject/${itemId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({reason: reason})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('拒绝成功');
                    loadReviewContent(); // 重新加载
                } else {
                    alert('拒绝失败: ' + data.error);
                }
            });
        }

        // 页面加载时执行搜索
        window.onload = function() {
            performSearch();
        };
        
        // 添加表单提交处理
        document.getElementById('addForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                id: document.getElementById('itemId').value,
                title: document.getElementById('itemTitle').value,
                description: document.getElementById('itemDescription').value,
                category: document.getElementById('itemCategory').value,
                tags: document.getElementById('itemTags').value.split(',').map(t => t.trim()).filter(t => t),
                content: {
                    summary: document.getElementById('itemSummary').value,
                    details: document.getElementById('itemDetails').value
                },
                metadata: {
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    version: '1.0.0',
                    author: 'Web界面用户'
                }
            };
            
            fetch('/api/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('添加成功！');
                    document.getElementById('addForm').reset();
                } else {
                    alert('添加失败: ' + data.error);
                }
            });
        });
    </script>
</body>
</html>
"""


@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)


@app.route('/api/search')
def api_search():
    """搜索API"""
    query = request.args.get('query', '')
    category = request.args.get('category', '')
    tags = request.args.get('tags', '')
    confidence_level = request.args.get('confidence_level', '')
    
    filters = {}
    if category:
        filters['category'] = category
    if tags:
        filters['tags'] = [tag.strip() for tag in tags.split(',') if tag.strip()]
    if confidence_level:
        filters['confidence_level'] = confidence_level
    
    results = manager.search_knowledge(query, **filters)
    return jsonify(results)


@app.route('/api/list')
def api_list():
    """列表API"""
    items = manager.list_all_items()
    return jsonify(items)


@app.route('/api/stats')
def api_stats():
    """统计API"""
    stats = manager.search_engine.get_statistics()
    return jsonify(stats)


@app.route('/api/add', methods=['POST'])
def api_add():
    """添加API"""
    try:
        data = request.json
        success = manager.add_knowledge_item(data)
        return jsonify({'success': success})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/item/<item_id>')
def api_get_item(item_id):
    """获取单个条目API"""
    item = manager.get_knowledge_item(item_id)
    if item:
        return jsonify(item)
    else:
        return jsonify({'error': '条目未找到'}), 404


# 自动更新相关API
@app.route('/api/auto-update/upload', methods=['POST'])
def api_upload_whatsapp_file():
    """上传WhatsApp导出文件API"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '未找到文件'}), 400

        file = request.files['file']
        group_id = request.form.get('group_id', 'unknown')

        if file.filename == '':
            return jsonify({'success': False, 'error': '未选择文件'}), 400

        # 保存上传的文件
        upload_dir = Path(__file__).parent.parent / "uploads"
        upload_dir.mkdir(exist_ok=True)

        file_path = upload_dir / file.filename
        file.save(str(file_path))

        # 异步处理文件
        import asyncio
        from auto_update_manager import AutoUpdateManager

        auto_manager = AutoUpdateManager()

        # 在后台处理文件
        def process_file():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    auto_manager.process_whatsapp_export(str(file_path), group_id)
                )
                return result
            finally:
                loop.close()

        # 这里应该使用任务队列，简化起见直接处理
        result = process_file()

        return jsonify({
            'success': True,
            'message': '文件处理完成',
            'result': result
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/auto-update/pending-reviews')
def api_get_pending_reviews():
    """获取待审核条目API"""
    try:
        from auto_update_manager import AutoUpdateManager
        auto_manager = AutoUpdateManager()

        pending_items = auto_manager.get_pending_reviews()
        return jsonify(pending_items)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/auto-update/approve/<item_id>', methods=['POST'])
def api_approve_review(item_id):
    """批准审核条目API"""
    try:
        from auto_update_manager import AutoUpdateManager
        auto_manager = AutoUpdateManager()

        success = auto_manager.approve_review_item(item_id)
        return jsonify({'success': success})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/auto-update/reject/<item_id>', methods=['POST'])
def api_reject_review(item_id):
    """拒绝审核条目API"""
    try:
        from auto_update_manager import AutoUpdateManager
        auto_manager = AutoUpdateManager()

        reason = request.json.get('reason', '') if request.json else ''
        success = auto_manager.reject_review_item(item_id, reason)

        return jsonify({'success': success})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/auto-update/statistics')
def api_get_auto_update_stats():
    """获取自动更新统计API"""
    try:
        from auto_update_manager import AutoUpdateManager
        auto_manager = AutoUpdateManager()

        stats = auto_manager.get_processing_statistics()
        return jsonify(stats)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


if __name__ == '__main__':
    print("启动知识库Web界面...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
