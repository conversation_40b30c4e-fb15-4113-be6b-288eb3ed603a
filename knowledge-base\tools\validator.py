#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库数据验证模块
提供数据格式验证、完整性检查和一致性验证功能
"""

import re
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import logging


class KnowledgeValidator:
    """知识库数据验证器"""
    
    def __init__(self, schema: Dict, tags_config: Dict):
        """
        初始化验证器
        
        Args:
            schema: JSON Schema规范
            tags_config: 标签配置
        """
        self.schema = schema
        self.tags_config = tags_config
        self.errors = []
        
        # 提取有效标签列表
        self.valid_tags = self._extract_valid_tags()
        
    def _extract_valid_tags(self) -> set:
        """提取所有有效标签"""
        valid_tags = set()
        
        # 从标签配置中提取
        for category, tag_groups in self.tags_config.items():
            if isinstance(tag_groups, dict):
                for group_name, tags in tag_groups.items():
                    if isinstance(tags, list):
                        valid_tags.update(tags)
        
        return valid_tags
    
    def validate_item(self, item: Dict) -> bool:
        """
        验证单个知识条目
        
        Args:
            item: 知识条目数据
            
        Returns:
            bool: 验证是否通过
        """
        self.errors = []
        
        # 基本结构验证
        if not self._validate_basic_structure(item):
            return False
        
        # 字段类型验证
        if not self._validate_field_types(item):
            return False
        
        # 标签验证
        if not self._validate_tags(item):
            return False
        
        # 关联关系验证
        if not self._validate_relationships(item):
            return False
        
        # 司机操守特定字段验证
        if item.get('category') in ['violations', 'regulations', 'penalties']:
            if not self._validate_driver_conduct_fields(item):
                return False
        
        return len(self.errors) == 0
    
    def _validate_basic_structure(self, item: Dict) -> bool:
        """验证基本结构"""
        required_fields = self.schema.get('required', [])
        
        for field in required_fields:
            if field not in item:
                self.errors.append(f"缺少必需字段: {field}")
                return False
        
        return True
    
    def _validate_field_types(self, item: Dict) -> bool:
        """验证字段类型"""
        properties = self.schema.get('properties', {})
        
        for field_name, field_schema in properties.items():
            if field_name not in item:
                continue
            
            field_value = item[field_name]
            expected_type = field_schema.get('type')
            
            # 类型检查
            if not self._check_field_type(field_value, expected_type):
                self.errors.append(f"字段 {field_name} 类型错误，期望 {expected_type}")
                continue
            
            # 字符串长度检查
            if expected_type == 'string':
                min_length = field_schema.get('minLength')
                max_length = field_schema.get('maxLength')
                
                if min_length and len(field_value) < min_length:
                    self.errors.append(f"字段 {field_name} 长度不足，最少 {min_length} 字符")
                
                if max_length and len(field_value) > max_length:
                    self.errors.append(f"字段 {field_name} 长度超限，最多 {max_length} 字符")
                
                # 模式匹配
                pattern = field_schema.get('pattern')
                if pattern and not re.match(pattern, field_value):
                    self.errors.append(f"字段 {field_name} 格式不正确")
            
            # 枚举值检查
            enum_values = field_schema.get('enum')
            if enum_values and field_value not in enum_values:
                self.errors.append(f"字段 {field_name} 值无效，允许值: {enum_values}")
        
        return len(self.errors) == 0
    
    def _check_field_type(self, value: Any, expected_type: str) -> bool:
        """检查字段类型"""
        if expected_type == 'string':
            return isinstance(value, str)
        elif expected_type == 'integer':
            return isinstance(value, int)
        elif expected_type == 'number':
            return isinstance(value, (int, float))
        elif expected_type == 'boolean':
            return isinstance(value, bool)
        elif expected_type == 'array':
            return isinstance(value, list)
        elif expected_type == 'object':
            return isinstance(value, dict)
        else:
            return True
    
    def _validate_tags(self, item: Dict) -> bool:
        """验证标签"""
        tags = item.get('tags', [])
        
        if not isinstance(tags, list):
            self.errors.append("标签必须是数组类型")
            return False
        
        # 检查标签格式
        tag_pattern = re.compile(r'^[a-z_]+$')
        for tag in tags:
            if not isinstance(tag, str):
                self.errors.append(f"标签必须是字符串: {tag}")
                continue
            
            if not tag_pattern.match(tag):
                self.errors.append(f"标签格式错误（只允许小写字母和下划线）: {tag}")
                continue
            
            # 检查标签是否在有效列表中（警告，不阻止）
            if tag not in self.valid_tags:
                logging.warning(f"使用了未定义的标签: {tag}")
        
        # 检查标签数量限制
        max_tags = 10  # 可以从配置中读取
        if len(tags) > max_tags:
            self.errors.append(f"标签数量超限，最多 {max_tags} 个")
        
        # 检查重复标签
        if len(tags) != len(set(tags)):
            self.errors.append("存在重复标签")
        
        return len(self.errors) == 0
    
    def _validate_relationships(self, item: Dict) -> bool:
        """验证关联关系"""
        relationships = item.get('relationships', {})
        
        if not isinstance(relationships, dict):
            self.errors.append("关联关系必须是对象类型")
            return False
        
        # 验证关联ID格式
        id_pattern = re.compile(r'^[a-zA-Z0-9_-]+$')
        
        for rel_type, rel_ids in relationships.items():
            if not isinstance(rel_ids, list):
                self.errors.append(f"关联关系 {rel_type} 必须是数组类型")
                continue
            
            for rel_id in rel_ids:
                if not isinstance(rel_id, str):
                    self.errors.append(f"关联ID必须是字符串: {rel_id}")
                    continue
                
                if not id_pattern.match(rel_id):
                    self.errors.append(f"关联ID格式错误: {rel_id}")
        
        return len(self.errors) == 0
    
    def _validate_driver_conduct_fields(self, item: Dict) -> bool:
        """验证司机操守特定字段"""
        driver_specific = item.get('driver_conduct_specific', {})
        
        if not isinstance(driver_specific, dict):
            return True  # 可选字段
        
        # 验证违规类型
        violation_type = driver_specific.get('violation_type')
        if violation_type:
            valid_violations = [
                'tardiness', 'no_show', 'service_attitude', 
                'capability_mismatch', 'safety_violation', 'communication_issue'
            ]
            if violation_type not in valid_violations:
                self.errors.append(f"无效的违规类型: {violation_type}")
        
        # 验证严重程度
        severity = driver_specific.get('severity_level')
        if severity:
            valid_severities = ['minor', 'moderate', 'serious', 'critical']
            if severity not in valid_severities:
                self.errors.append(f"无效的严重程度: {severity}")
        
        # 验证处罚类型
        penalty_type = driver_specific.get('penalty_type')
        if penalty_type:
            valid_penalties = [
                'warning', 'financial_penalty', 'service_suspension', 'permanent_ban'
            ]
            if penalty_type not in valid_penalties:
                self.errors.append(f"无效的处罚类型: {penalty_type}")
        
        # 验证证据类型
        evidence_types = driver_specific.get('evidence_types', [])
        if evidence_types:
            valid_evidence = [
                'screenshot', 'audio_recording', 'customer_complaint',
                'system_record', 'witness_statement'
            ]
            for evidence in evidence_types:
                if evidence not in valid_evidence:
                    self.errors.append(f"无效的证据类型: {evidence}")
        
        return len(self.errors) == 0
    
    def _validate_metadata(self, item: Dict) -> bool:
        """验证元数据"""
        metadata = item.get('metadata', {})
        
        if not isinstance(metadata, dict):
            self.errors.append("元数据必须是对象类型")
            return False
        
        # 验证日期格式
        date_fields = ['created_at', 'updated_at']
        for field in date_fields:
            if field in metadata:
                try:
                    datetime.fromisoformat(metadata[field].replace('Z', '+00:00'))
                except ValueError:
                    self.errors.append(f"日期格式错误: {field}")
        
        # 验证版本号格式
        version = metadata.get('version')
        if version:
            version_pattern = re.compile(r'^\d+\.\d+\.\d+$')
            if not version_pattern.match(version):
                self.errors.append(f"版本号格式错误: {version}")
        
        return len(self.errors) == 0
    
    def validate_batch(self, items: List[Dict]) -> Tuple[List[str], List[str]]:
        """
        批量验证知识条目
        
        Args:
            items: 知识条目列表
            
        Returns:
            Tuple: (有效条目ID列表, 无效条目ID列表)
        """
        valid_items = []
        invalid_items = []
        
        for item in items:
            if self.validate_item(item):
                valid_items.append(item.get('id', 'unknown'))
            else:
                invalid_items.append(item.get('id', 'unknown'))
                logging.error(f"条目验证失败 {item.get('id')}: {self.errors}")
        
        return valid_items, invalid_items
    
    def get_validation_errors(self) -> List[str]:
        """获取最近的验证错误"""
        return self.errors.copy()
    
    def check_data_consistency(self, items: List[Dict]) -> Dict[str, List[str]]:
        """
        检查数据一致性
        
        Args:
            items: 知识条目列表
            
        Returns:
            Dict: 一致性检查结果
        """
        issues = {
            'duplicate_ids': [],
            'broken_relationships': [],
            'orphaned_items': [],
            'circular_references': []
        }
        
        # 检查重复ID
        id_counts = {}
        for item in items:
            item_id = item.get('id')
            if item_id:
                id_counts[item_id] = id_counts.get(item_id, 0) + 1
        
        issues['duplicate_ids'] = [item_id for item_id, count in id_counts.items() if count > 1]
        
        # 检查断开的关联关系
        all_ids = set(item.get('id') for item in items if item.get('id'))
        
        for item in items:
            relationships = item.get('relationships', {})
            for rel_type, rel_ids in relationships.items():
                for rel_id in rel_ids:
                    if rel_id not in all_ids:
                        issues['broken_relationships'].append(f"{item.get('id')} -> {rel_id}")
        
        return issues
