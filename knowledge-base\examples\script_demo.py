#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本架构演示
展示如何使用重构后的传统脚本架构进行知识库处理
"""

import sys
import os
import tempfile
from pathlib import Path
from datetime import datetime

# 添加scripts路径
sys.path.append(str(Path(__file__).parent.parent / "scripts"))

from main_processor import KnowledgeBaseProcessor


def create_sample_whatsapp_data():
    """创建示例WhatsApp数据文件"""
    
    # 客服团队群组数据
    group2_data = """
12/7/2024, 2:30 PM - 客服主管: 订单107480需要特定人员@60124088411处理
12/7/2024, 2:31 PM - 客服主管: 这是婴儿椅订单，请确保司机有相关设备
12/7/2024, 2:35 PM - 客服A: 收到，已联系司机确认设备情况
12/7/2024, 2:40 PM - 客服A: 司机确认有婴儿椅设备，订单已成功分配
12/7/2024, 3:15 PM - 客服主管: 订单价格需要调整，客户要求从$25调整到$30
12/7/2024, 3:16 PM - 客服主管: 调整原因：路线变更，增加了10公里距离
12/7/2024, 3:20 PM - 客服B: 已在系统中调整价格，客户已确认新价格
12/7/2024, 4:00 PM - 系统管理员: 后台监控显示订单处理时间超过平均值
12/7/2024, 4:01 PM - 系统管理员: 建议优化订单分配算法提高效率
12/7/2024, 4:05 PM - 技术负责人: 已记录问题，将在下次系统更新中优化
12/7/2024, 5:30 PM - 客服主管: 客户对服务很满意，给了5星好评
12/7/2024, 5:31 PM - 客服主管: 司机@60124088411表现优秀，值得表扬
    """
    
    # 司机问题处理群组数据
    group7_data = """
12/7/2024, 10:00 AM - 运营主管: 司机DENISWEECHEESIONG今天迟到30分钟
12/7/2024, 10:01 AM - 运营主管: 这是本月第3次迟到，需要按规定处理
12/7/2024, 10:05 AM - 客服主管: 已收到客户投诉，对迟到很不满意
12/7/2024, 10:10 AM - 运营主管: 根据处罚标准，第3次迟到罚款$50
12/7/2024, 10:15 AM - 运营主管: 同时暂停服务1天，要求参加服务态度培训
12/7/2024, 10:20 AM - 人事部: 已安排明天的服务态度培训课程
12/7/2024, 11:00 AM - 运营主管: 司机@60124088411无法提供婴儿椅服务
12/7/2024, 11:01 AM - 运营主管: 但接了婴儿椅订单，这是能力不匹配问题
12/7/2024, 11:05 AM - 客服主管: 客户很不满意，要求退款和道歉
12/7/2024, 11:10 AM - 运营主管: 已安排其他司机，给客户道歉并提供补偿
12/7/2024, 2:00 PM - 质量监督: 需要加强司机能力评估和订单匹配
12/7/2024, 2:01 PM - 质量监督: 建议建立司机技能档案系统
    """
    
    # 各部门信息交流群组数据
    group8_data = """
12/7/2024, 9:00 AM - IT部门: 系统将在今晚进行维护更新
12/7/2024, 9:01 AM - IT部门: 预计停机时间2小时，请各部门做好准备
12/7/2024, 9:30 AM - 运营部: 收到，会提前通知所有司机
12/7/2024, 9:31 AM - 客服部: 会在网站发布维护公告
12/7/2024, 10:00 AM - 财务部: 本月司机处罚金额统计已完成
12/7/2024, 10:01 AM - 财务部: 总计$500，主要是迟到和服务态度问题
12/7/2024, 11:00 AM - 人事部: 新司机培训计划已制定
12/7/2024, 11:01 AM - 人事部: 重点加强服务标准和设备使用培训
12/7/2024, 2:00 PM - 质量部: 客户满意度调查结果出来了
12/7/2024, 2:01 PM - 质量部: 整体评分4.2/5.0，主要问题是司机迟到
12/7/2024, 3:00 PM - 技术部: 订单分配算法优化完成
12/7/2024, 3:01 PM - 技术部: 新算法可以减少30%的处理时间
    """
    
    # 创建临时文件
    temp_dir = Path(tempfile.mkdtemp())
    
    files = {
        'group2_chat.txt': group2_data,
        'group7_chat.txt': group7_data, 
        'group8_chat.txt': group8_data
    }
    
    file_paths = []
    for filename, content in files.items():
        file_path = temp_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content.strip())
        file_paths.append(str(file_path))
    
    return file_paths, temp_dir


def demo_basic_processing():
    """演示基本处理流程"""
    print("=== 演示1: 基本脚本处理流程 ===")
    
    # 创建示例数据
    file_paths, temp_dir = create_sample_whatsapp_data()
    
    try:
        # 初始化处理器
        processor = KnowledgeBaseProcessor()
        
        print(f"创建了 {len(file_paths)} 个示例文件:")
        for path in file_paths:
            print(f"  - {Path(path).name}")
        
        # 定义群组映射
        group_mappings = {
            file_paths[0]: 'group_2',  # 客服团队
            file_paths[1]: 'group_7',  # 司机问题处理
            file_paths[2]: 'group_8'   # 各部门信息交流
        }
        
        print("\n开始处理文件...")
        
        # 处理文件
        result = processor.process_whatsapp_files(file_paths, group_mappings)
        
        if result['success']:
            print("✅ 处理成功完成！")
            
            # 显示统计信息
            stats = result['statistics']
            print(f"\n📊 处理统计:")
            print(f"  - 总消息数: {stats['total_messages']}")
            print(f"  - 处理消息数: {stats['processed_messages']}")
            print(f"  - 提取知识条目: {stats['extracted_knowledge']}")
            print(f"  - 生成标签数: {stats['generated_tags']}")
            print(f"  - 识别实体数: {stats['identified_entities']}")
            print(f"  - 网络节点数: {stats['network_nodes']}")
            print(f"  - 网络边数: {stats['network_edges']}")
            
            # 显示输出文件
            print(f"\n📁 生成的文件:")
            for file_type, file_path in result['output_files'].items():
                print(f"  - {file_type}: {file_path}")
            
            # 显示处理报告摘要
            report = result['report']
            duration = report['processing_summary']['duration_seconds']
            if duration:
                print(f"\n⏱️ 处理耗时: {duration:.2f} 秒")
            
            quality_metrics = report['quality_metrics']
            print(f"📈 质量指标:")
            print(f"  - 处理成功率: {quality_metrics['processing_rate']:.2%}")
            print(f"  - 知识提取率: {quality_metrics['knowledge_extraction_rate']:.2%}")
            print(f"  - 平均标签数: {quality_metrics['average_tags_per_message']:.1f}")
            
        else:
            print("❌ 处理失败！")
            print(f"错误: {result['error']}")
    
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        import shutil
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 已清理临时文件")
        except:
            pass
    
    print()


def demo_visualization_output():
    """演示可视化输出"""
    print("=== 演示2: 可视化输出生成 ===")
    
    # 检查是否有生成的静态文件
    base_path = Path(__file__).parent.parent
    static_path = base_path / "static"
    
    if static_path.exists():
        print("📊 检查生成的可视化文件:")
        
        # 检查HTML文件
        html_files = ['dashboard.html', 'knowledge_network.html', 'trend_analysis.html']
        for html_file in html_files:
            file_path = static_path / html_file
            if file_path.exists():
                print(f"  ✅ {html_file} - {file_path}")
            else:
                print(f"  ❌ {html_file} - 未找到")
        
        # 检查数据文件
        data_path = static_path / "data"
        if data_path.exists():
            print(f"\n📁 数据文件目录: {data_path}")
            data_files = list(data_path.glob("*.json"))
            for data_file in data_files:
                print(f"  📄 {data_file.name}")
        
        # 检查CSS和JS文件
        css_path = static_path / "css" / "dashboard.css"
        js_path = static_path / "js" / "dashboard.js"
        
        if css_path.exists():
            print(f"  🎨 CSS文件: {css_path}")
        if js_path.exists():
            print(f"  ⚡ JS文件: {js_path}")
        
        # 提供访问说明
        dashboard_path = static_path / "dashboard.html"
        if dashboard_path.exists():
            print(f"\n🌐 可以通过以下方式访问仪表盘:")
            print(f"  - 文件路径: file://{dashboard_path.absolute()}")
            print(f"  - 或在浏览器中打开: {dashboard_path}")
    
    else:
        print("❌ 静态文件目录不存在")
        print("请先运行处理脚本生成可视化文件")
    
    print()


def demo_command_line_usage():
    """演示命令行使用方法"""
    print("=== 演示3: 命令行使用方法 ===")
    
    print("📝 命令行使用示例:")
    
    # 基本用法
    print("\n1. 基本用法:")
    print("   python scripts/main_processor.py file1.txt file2.txt")
    
    # 带配置文件
    print("\n2. 指定配置文件:")
    print("   python scripts/main_processor.py --config config/custom_config.json file1.txt")
    
    # 带群组映射
    print("\n3. 指定群组映射:")
    print("   python scripts/main_processor.py --group-mapping group_mapping.json file1.txt file2.txt")
    
    # 详细输出
    print("\n4. 详细输出模式:")
    print("   python scripts/main_processor.py --verbose file1.txt")
    
    # 自动打开浏览器
    print("\n5. 处理完成后自动打开浏览器:")
    print("   python scripts/main_processor.py --open-browser file1.txt")
    
    # 群组映射文件示例
    print("\n📄 群组映射文件示例 (group_mapping.json):")
    mapping_example = {
        "path/to/customer_service_chat.txt": "group_2",
        "path/to/driver_issues_chat.txt": "group_7",
        "path/to/department_chat.txt": "group_8"
    }
    print("   " + str(mapping_example).replace("'", '"'))
    
    print()


def demo_output_structure():
    """演示输出结构"""
    print("=== 演示4: 输出文件结构 ===")
    
    base_path = Path(__file__).parent.parent
    
    print("📁 完整的输出文件结构:")
    print(f"""
{base_path}/
├── output/                          # 处理结果输出目录
│   ├── knowledge_base.json          # 结构化知识库文件
│   ├── processing_report.json       # 处理报告
│   ├── processing_YYYYMMDD_HHMMSS.log # 处理日志
│   └── visualization_data/          # 可视化数据文件
│       ├── dashboard_stats.json
│       ├── knowledge_distribution.json
│       ├── time_trends.json
│       ├── tag_analysis.json
│       ├── entity_statistics.json
│       └── driver_conduct_stats.json
├── static/                          # 静态可视化文件
│   ├── dashboard.html               # 主仪表盘
│   ├── knowledge_network.html       # 知识网络可视化
│   ├── trend_analysis.html          # 趋势分析页面
│   ├── css/
│   │   └── dashboard.css            # 样式文件
│   ├── js/
│   │   ├── dashboard.js             # 仪表盘脚本
│   │   ├── network.js               # 网络可视化脚本
│   │   └── trends.js                # 趋势分析脚本
│   └── data/                        # 前端数据文件
│       ├── dashboard_stats.json
│       ├── knowledge_distribution.json
│       ├── network.json
│       └── ...
    """)
    
    print("📋 主要文件说明:")
    print("  📊 knowledge_base.json - 符合现有schema的结构化知识库")
    print("  📈 dashboard.html - 交互式数据可视化仪表盘") 
    print("  🕸️ knowledge_network.html - 实体关联网络图谱")
    print("  📉 trend_analysis.html - 时间趋势分析图表")
    print("  📄 processing_report.json - 详细的处理统计报告")
    
    print()


def main():
    """主演示函数"""
    print("🚀 知识库脚本架构演示")
    print("=" * 50)
    
    print("本演示展示了重构后的传统脚本架构的主要功能:")
    print("1. 独立的Python脚本执行模式")
    print("2. 静态HTML可视化仪表盘")
    print("3. 多维度标签和实体提取")
    print("4. 知识网络关系图谱")
    print("5. 完整的处理报告生成")
    print()
    
    try:
        # 运行各个演示
        demo_basic_processing()
        demo_visualization_output()
        demo_command_line_usage()
        demo_output_structure()
        
        print("✅ 所有演示完成！")
        print("\n🎯 下一步操作建议:")
        print("1. 准备真实的WhatsApp导出文件")
        print("2. 运行: python scripts/main_processor.py your_files.txt")
        print("3. 在浏览器中打开生成的HTML仪表盘")
        print("4. 查看生成的知识库JSON文件")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
