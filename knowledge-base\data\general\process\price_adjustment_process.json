{"id": "price_adjustment_process_001", "title": "订单价格调整流程", "description": "当订单需要价格调整时的标准操作流程，包括调价原因判断、调价执行和后续处理", "category": "process", "subcategory": "pricing", "tags": ["pricing", "order_processing", "customer_service", "important", "financial"], "content": {"summary": "订单价格调整的完整流程，确保价格调整的合理性和及时性", "details": "价格调整是订单处理中的重要环节，通常由特定人员负责。调价原因包括特殊服务要求、市场价格波动、第三方平台政策等。调价后需要重新投放司机池或通知相关司机。", "examples": ["订单107233需要协助调价丢POOL @27462104813640", "订单107463急单需要调价丢pool @27462104813640", "飞猪送机单9:10am的需要调价处理"], "related_cases": ["第三方平台急单调价案例", "特殊服务要求调价案例", "市场价格波动调价案例"], "steps": [{"step": 1, "description": "识别调价需求", "notes": "根据订单特殊要求或市场情况判断是否需要调价"}, {"step": 2, "description": "联系价格调整负责人", "notes": "通过@27462104813640联系价格调整负责人"}, {"step": 3, "description": "确定调价幅度", "notes": "根据具体情况确定合理的价格调整幅度"}, {"step": 4, "description": "执行价格调整", "notes": "在系统中更新订单价格"}, {"step": 5, "description": "重新投放司机池", "notes": "调价完成后将订单重新投放到司机池"}, {"step": 6, "description": "跟踪后续处理", "notes": "确保调价后的订单能够正常被司机接受"}]}, "metadata": {"created_at": "2025-07-09T10:15:00Z", "updated_at": "2025-07-09T10:15:00Z", "version": "1.0.0", "author": "系统管理员", "last_editor": "系统管理员", "source": "WhatsApp群组2客服团队聊天记录", "confidence_level": "high", "review_status": "approved", "expiry_date": null}, "relationships": {"related_items": ["order_processing_workflow_001", "third_party_platform_handling_001", "emergency_order_handling_001"], "prerequisites": ["pricing_policy_understanding_001"], "follow_ups": ["driver_pool_management_001"], "supersedes": []}}