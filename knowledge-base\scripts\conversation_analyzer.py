#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话分析器
负责WhatsApp对话的深度分析，包括多维度标签生成和知识提取
"""

import re
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import sys

# 添加tools路径
sys.path.append(str(Path(__file__).parent.parent / "tools"))

from whatsapp_parser import WhatsAppMessageParser
from gemini_integration import GeminiKnowledgeExtractor


class ConversationAnalyzer:
    """对话分析器"""
    
    def __init__(self, config: Dict):
        """
        初始化对话分析器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger('ConversationAnalyzer')
        
        # 初始化组件
        self.whatsapp_parser = WhatsAppMessageParser()
        
        # 初始化Gemini API（如果有密钥）
        self.gemini_extractor = None
        if self._has_gemini_api():
            try:
                import os
                self.gemini_extractor = GeminiKnowledgeExtractor(
                    os.getenv('GEMINI_API_KEY'), 
                    config
                )
            except Exception as e:
                self.logger.warning(f"Gemini API初始化失败: {e}")
        
        # 预定义标签和模式
        self.business_patterns = self._load_business_patterns()
        self.sentiment_patterns = self._load_sentiment_patterns()
        self.urgency_patterns = self._load_urgency_patterns()
    
    def _has_gemini_api(self) -> bool:
        """检查是否有Gemini API密钥"""
        import os
        return bool(os.getenv('GEMINI_API_KEY'))
    
    def _load_business_patterns(self) -> Dict[str, List[str]]:
        """加载业务类型识别模式"""
        return {
            'order_processing': [
                r'订单\d+', r'order\s*\d+', r'处理订单', r'分配订单', r'订单状态',
                r'价格调整', r'路线变更', r'订单确认', r'订单取消'
            ],
            'driver_management': [
                r'司机\w+', r'driver\s*\w+', r'@\d{8,15}', r'迟到', r'违规',
                r'处罚', r'暂停服务', r'培训', r'考核', r'投诉'
            ],
            'customer_service': [
                r'客户\w+', r'customer\s*\w+', r'投诉', r'表扬', r'退款',
                r'补偿', r'道歉', r'客服', r'服务态度', r'满意度'
            ],
            'technical_support': [
                r'系统\w+', r'system\s*\w+', r'后台', r'监控', r'报警',
                r'故障', r'维护', r'更新', r'优化', r'bug'
            ],
            'policy_update': [
                r'政策\w+', r'policy\s*\w+', r'规定', r'标准', r'流程',
                r'制度', r'通知', r'公告', r'变更', r'调整'
            ],
            'training': [
                r'培训\w+', r'training\s*\w+', r'学习', r'课程', r'考试',
                r'认证', r'技能', r'知识', r'教育', r'指导'
            ]
        }
    
    def _load_sentiment_patterns(self) -> Dict[str, List[str]]:
        """加载情感分析模式"""
        return {
            'positive': [
                r'好的', r'没问题', r'收到', r'明白', r'同意', r'支持',
                r'赞同', r'满意', r'表扬', r'感谢', r'谢谢'
            ],
            'negative': [
                r'不行', r'不可以', r'拒绝', r'反对', r'不满', r'生气',
                r'愤怒', r'失望', r'糟糕', r'问题', r'错误'
            ],
            'neutral': [
                r'知道了', r'了解', r'记录', r'确认', r'检查', r'查看',
                r'处理', r'安排', r'联系', r'通知', r'更新'
            ],
            'complaint': [
                r'投诉', r'抱怨', r'不满意', r'差评', r'问题', r'故障',
                r'延误', r'迟到', r'态度差', r'服务不好'
            ],
            'praise': [
                r'表扬', r'好评', r'满意', r'优秀', r'棒', r'赞',
                r'服务好', r'态度好', r'专业', r'及时'
            ]
        }
    
    def _load_urgency_patterns(self) -> Dict[str, List[str]]:
        """加载紧急程度识别模式"""
        return {
            'urgent': [
                r'紧急', r'urgent', r'立即', r'马上', r'赶快', r'尽快',
                r'重要', r'严重', r'critical', r'emergency'
            ],
            'important': [
                r'重要', r'important', r'优先', r'priority', r'关键',
                r'必须', r'需要', r'要求', r'注意'
            ],
            'normal': [
                r'一般', r'normal', r'常规', r'普通', r'标准',
                r'正常', r'平时', r'通常'
            ],
            'low': [
                r'不急', r'慢慢', r'有时间', r'方便时', r'later',
                r'低优先级', r'可选', r'建议'
            ]
        }
    
    def analyze_file(self, file_path: str, group_id: str) -> List[Dict]:
        """
        分析WhatsApp文件
        
        Args:
            file_path: 文件路径
            group_id: 群组ID
            
        Returns:
            List[Dict]: 分析后的对话列表
        """
        self.logger.info(f"开始分析文件: {file_path}")
        
        try:
            # 解析消息
            messages = self.whatsapp_parser.parse_file(file_path)
            self.logger.info(f"解析到 {len(messages)} 条消息")
            
            # 转换为对话格式并分析
            conversations = []
            for message in messages:
                if message.get('message_type') == 'text':  # 只处理文本消息
                    conversation = self._message_to_conversation(message, group_id)
                    if conversation:
                        conversations.append(conversation)
            
            self.logger.info(f"转换为 {len(conversations)} 条对话")
            return conversations
            
        except Exception as e:
            self.logger.error(f"分析文件失败 {file_path}: {e}")
            return []
    
    def _message_to_conversation(self, message: Dict, group_id: str) -> Optional[Dict]:
        """将消息转换为对话格式"""
        try:
            conversation = {
                'id': f"{group_id}_{message.get('timestamp', '')}_{hash(message.get('content', ''))}",
                'group_id': group_id,
                'timestamp': message.get('timestamp'),
                'sender': message.get('sender'),
                'content': message.get('content'),
                'original_message': message,
                'analysis': {
                    'processed_at': datetime.now().isoformat(),
                    'content_length': len(message.get('content', '')),
                    'word_count': len(message.get('content', '').split())
                }
            }
            
            return conversation
            
        except Exception as e:
            self.logger.error(f"转换消息失败: {e}")
            return None
    
    def generate_tags(self, conversation: Dict) -> List[Dict]:
        """
        为对话生成多维度标签
        
        Args:
            conversation: 对话数据
            
        Returns:
            List[Dict]: 标签列表
        """
        content = conversation.get('content', '').lower()
        tags = []
        
        # 1. 业务类型标签
        business_tags = self._extract_business_tags(content)
        tags.extend(business_tags)
        
        # 2. 情感标签
        sentiment_tags = self._extract_sentiment_tags(content)
        tags.extend(sentiment_tags)
        
        # 3. 紧急程度标签
        urgency_tags = self._extract_urgency_tags(content)
        tags.extend(urgency_tags)
        
        # 4. 实体标签（基于内容）
        entity_tags = self._extract_entity_tags(content)
        tags.extend(entity_tags)
        
        # 5. 时间标签
        time_tags = self._extract_time_tags(conversation)
        tags.extend(time_tags)
        
        return tags
    
    def _extract_business_tags(self, content: str) -> List[Dict]:
        """提取业务类型标签"""
        tags = []
        
        for business_type, patterns in self.business_patterns.items():
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    tags.append({
                        'type': 'business',
                        'value': business_type,
                        'confidence': 0.8,
                        'source': 'pattern_match'
                    })
                    break  # 每种业务类型只添加一次
        
        return tags
    
    def _extract_sentiment_tags(self, content: str) -> List[Dict]:
        """提取情感标签"""
        tags = []
        sentiment_scores = {}
        
        for sentiment, patterns in self.sentiment_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, content, re.IGNORECASE))
                score += matches
            sentiment_scores[sentiment] = score
        
        # 选择得分最高的情感
        if sentiment_scores:
            max_sentiment = max(sentiment_scores, key=sentiment_scores.get)
            if sentiment_scores[max_sentiment] > 0:
                tags.append({
                    'type': 'sentiment',
                    'value': max_sentiment,
                    'confidence': min(sentiment_scores[max_sentiment] * 0.3, 1.0),
                    'source': 'pattern_match'
                })
        
        return tags
    
    def _extract_urgency_tags(self, content: str) -> List[Dict]:
        """提取紧急程度标签"""
        tags = []
        urgency_scores = {}
        
        for urgency, patterns in self.urgency_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, content, re.IGNORECASE))
                score += matches
            urgency_scores[urgency] = score
        
        # 选择得分最高的紧急程度
        if urgency_scores:
            max_urgency = max(urgency_scores, key=urgency_scores.get)
            if urgency_scores[max_urgency] > 0:
                tags.append({
                    'type': 'urgency',
                    'value': max_urgency,
                    'confidence': min(urgency_scores[max_urgency] * 0.4, 1.0),
                    'source': 'pattern_match'
                })
        
        return tags
    
    def _extract_entity_tags(self, content: str) -> List[Dict]:
        """提取实体标签"""
        tags = []
        
        # 订单号
        order_matches = re.findall(r'订单\s*(\d+)', content, re.IGNORECASE)
        for order_id in order_matches:
            tags.append({
                'type': 'entity',
                'value': f'order_{order_id}',
                'confidence': 0.9,
                'source': 'regex_extraction'
            })
        
        # 司机ID（电话号码格式）
        driver_matches = re.findall(r'@?(\d{8,15})', content)
        for driver_id in driver_matches:
            tags.append({
                'type': 'entity',
                'value': f'driver_{driver_id}',
                'confidence': 0.8,
                'source': 'regex_extraction'
            })
        
        # 金额
        amount_matches = re.findall(r'\$(\d+(?:\.\d{2})?)', content)
        for amount in amount_matches:
            tags.append({
                'type': 'entity',
                'value': f'amount_{amount}',
                'confidence': 0.7,
                'source': 'regex_extraction'
            })
        
        return tags
    
    def _extract_time_tags(self, conversation: Dict) -> List[Dict]:
        """提取时间相关标签"""
        tags = []
        
        try:
            timestamp = conversation.get('timestamp')
            if timestamp:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                
                # 时间段标签
                hour = dt.hour
                if 6 <= hour < 12:
                    time_period = 'morning'
                elif 12 <= hour < 18:
                    time_period = 'afternoon'
                elif 18 <= hour < 22:
                    time_period = 'evening'
                else:
                    time_period = 'night'
                
                tags.append({
                    'type': 'time',
                    'value': time_period,
                    'confidence': 1.0,
                    'source': 'timestamp_analysis'
                })
                
                # 工作日/周末标签
                weekday = dt.weekday()
                day_type = 'weekday' if weekday < 5 else 'weekend'
                
                tags.append({
                    'type': 'time',
                    'value': day_type,
                    'confidence': 1.0,
                    'source': 'timestamp_analysis'
                })
        
        except Exception as e:
            self.logger.warning(f"时间标签提取失败: {e}")
        
        return tags
    
    def extract_knowledge_items(self, conversations: List[Dict]) -> List[Dict]:
        """
        从对话中提取知识条目
        
        Args:
            conversations: 对话列表
            
        Returns:
            List[Dict]: 知识条目列表
        """
        knowledge_items = []
        
        # 按群组分组处理
        grouped_conversations = self._group_conversations_by_context(conversations)
        
        for group_key, conv_group in grouped_conversations.items():
            # 尝试从对话组中提取知识
            extracted_items = self._extract_knowledge_from_group(conv_group)
            knowledge_items.extend(extracted_items)
        
        self.logger.info(f"从 {len(conversations)} 条对话中提取了 {len(knowledge_items)} 个知识条目")
        return knowledge_items
    
    def _group_conversations_by_context(self, conversations: List[Dict]) -> Dict[str, List[Dict]]:
        """按上下文对对话进行分组"""
        groups = {}
        
        for conversation in conversations:
            # 基于时间窗口和内容相似性分组
            group_key = self._determine_conversation_group(conversation, groups)
            
            if group_key not in groups:
                groups[group_key] = []
            groups[group_key].append(conversation)
        
        return groups
    
    def _determine_conversation_group(self, conversation: Dict, existing_groups: Dict) -> str:
        """确定对话所属的组"""
        # 简单的分组策略：基于群组ID和时间窗口
        group_id = conversation.get('group_id', 'unknown')
        timestamp = conversation.get('timestamp', '')
        
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            # 按小时分组
            time_key = dt.strftime('%Y%m%d_%H')
            return f"{group_id}_{time_key}"
        except:
            return f"{group_id}_unknown"
    
    def _extract_knowledge_from_group(self, conversations: List[Dict]) -> List[Dict]:
        """从对话组中提取知识条目"""
        knowledge_items = []
        
        # 如果有Gemini API，使用AI提取
        if self.gemini_extractor and len(conversations) > 1:
            try:
                # 合并对话内容
                combined_content = self._combine_conversations(conversations)
                
                # 使用AI提取知识
                # 这里需要实现异步调用的同步版本
                # extracted = await self.gemini_extractor.extract_knowledge(combined_content, conversations[0].get('group_id'))
                # if extracted:
                #     knowledge_items.append(extracted)
                
            except Exception as e:
                self.logger.warning(f"AI知识提取失败: {e}")
        
        # 基于规则的知识提取
        rule_based_items = self._rule_based_knowledge_extraction(conversations)
        knowledge_items.extend(rule_based_items)
        
        return knowledge_items
    
    def _combine_conversations(self, conversations: List[Dict]) -> str:
        """合并对话内容"""
        combined = []
        for conv in conversations:
            sender = conv.get('sender', 'Unknown')
            content = conv.get('content', '')
            timestamp = conv.get('timestamp', '')
            combined.append(f"[{timestamp}] {sender}: {content}")
        
        return '\n'.join(combined)
    
    def _rule_based_knowledge_extraction(self, conversations: List[Dict]) -> List[Dict]:
        """基于规则的知识提取"""
        knowledge_items = []
        
        # 检查是否包含问题-解决方案模式
        problem_solution_items = self._extract_problem_solution_patterns(conversations)
        knowledge_items.extend(problem_solution_items)
        
        # 检查是否包含流程描述
        process_items = self._extract_process_patterns(conversations)
        knowledge_items.extend(process_items)
        
        # 检查是否包含违规处理
        violation_items = self._extract_violation_patterns(conversations)
        knowledge_items.extend(violation_items)
        
        return knowledge_items
    
    def _extract_problem_solution_patterns(self, conversations: List[Dict]) -> List[Dict]:
        """提取问题-解决方案模式"""
        # 简化实现：寻找包含问题和解决方案关键词的对话组
        problem_keywords = ['问题', '故障', '错误', '失败', '不能', '无法']
        solution_keywords = ['解决', '处理', '修复', '完成', '好了', '成功']
        
        has_problem = any(
            any(keyword in conv.get('content', '') for keyword in problem_keywords)
            for conv in conversations
        )
        
        has_solution = any(
            any(keyword in conv.get('content', '') for keyword in solution_keywords)
            for conv in conversations
        )
        
        if has_problem and has_solution and len(conversations) >= 2:
            # 创建知识条目
            item_id = f"extracted_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(conversations))}"
            
            return [{
                'id': item_id,
                'title': '问题处理记录',
                'description': '从对话中提取的问题处理流程',
                'category': 'process',
                'tags': ['extracted', 'problem_solving'],
                'content': {
                    'summary': '问题识别和解决过程',
                    'details': self._combine_conversations(conversations)
                },
                'metadata': {
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'author': '自动提取系统',
                    'source': 'conversation_analysis',
                    'confidence_level': 'medium',
                    'extraction_method': 'rule_based'
                }
            }]
        
        return []
    
    def _extract_process_patterns(self, conversations: List[Dict]) -> List[Dict]:
        """提取流程模式"""
        # 寻找包含步骤描述的对话
        step_keywords = ['第一', '第二', '第三', '首先', '然后', '最后', '步骤', '流程']
        
        has_steps = any(
            any(keyword in conv.get('content', '') for keyword in step_keywords)
            for conv in conversations
        )
        
        if has_steps and len(conversations) >= 2:
            item_id = f"process_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(conversations))}"
            
            return [{
                'id': item_id,
                'title': '操作流程记录',
                'description': '从对话中提取的操作流程',
                'category': 'process',
                'tags': ['extracted', 'workflow'],
                'content': {
                    'summary': '操作步骤和流程说明',
                    'details': self._combine_conversations(conversations)
                },
                'metadata': {
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'author': '自动提取系统',
                    'source': 'conversation_analysis',
                    'confidence_level': 'medium',
                    'extraction_method': 'rule_based'
                }
            }]
        
        return []
    
    def _extract_violation_patterns(self, conversations: List[Dict]) -> List[Dict]:
        """提取违规处理模式"""
        violation_keywords = ['违规', '迟到', '投诉', '处罚', '罚款', '暂停']
        
        has_violation = any(
            any(keyword in conv.get('content', '') for keyword in violation_keywords)
            for conv in conversations
        )
        
        if has_violation and any(conv.get('group_id') == 'group_7' for conv in conversations):
            item_id = f"violation_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(conversations))}"
            
            return [{
                'id': item_id,
                'title': '违规处理记录',
                'description': '从对话中提取的违规处理案例',
                'category': 'violations',
                'tags': ['extracted', 'driver_conduct'],
                'content': {
                    'summary': '违规行为和处理措施',
                    'details': self._combine_conversations(conversations)
                },
                'metadata': {
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'author': '自动提取系统',
                    'source': 'conversation_analysis',
                    'confidence_level': 'medium',
                    'extraction_method': 'rule_based'
                },
                'driver_conduct_specific': {
                    'violation_type': 'unknown',
                    'severity_level': 'moderate',
                    'evidence_types': ['conversation_record']
                }
            }]
        
        return []
