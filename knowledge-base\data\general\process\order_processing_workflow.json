{"id": "order_processing_workflow_001", "title": "订单处理标准流程", "description": "客服团队处理订单的标准操作流程，包括订单分发、状态跟踪和问题处理", "category": "process", "subcategory": "order_management", "tags": ["order_processing", "customer_service", "workflow", "standard_procedure", "important"], "content": {"summary": "客服团队处理订单的完整流程，从订单接收到完成确认的标准操作步骤", "details": "订单处理流程是客服团队的核心工作流程，确保每个订单都能得到及时、准确的处理。流程包括订单接收、信息确认、司机分配、状态跟踪和完成确认等关键步骤。", "examples": ["订单107480需要特定人员@60124088411处理", "订单106320顾客状态更新：'顾客出来了' -> '已通知'", "订单107233需要调价后投放司机池"], "related_cases": ["飞猪平台急单处理案例", "价格调整流程案例", "第三方平台订单处理案例"], "steps": [{"step": 1, "description": "接收订单信息", "notes": "系统自动分发或手动接收订单"}, {"step": 2, "description": "确认订单详情", "notes": "确认车型、时间、地点等关键信息"}, {"step": 3, "description": "判断是否需要调价", "notes": "根据订单特殊要求或市场情况决定是否调价"}, {"step": 4, "description": "投放司机池或指定司机", "notes": "一般订单投放司机池，特殊订单可指定特定司机"}, {"step": 5, "description": "跟踪订单状态", "notes": "实时监控订单进展，及时处理异常情况"}, {"step": 6, "description": "确认订单完成", "notes": "收到完成通知后确认订单状态"}]}, "metadata": {"created_at": "2025-07-09T10:00:00Z", "updated_at": "2025-07-09T10:00:00Z", "version": "1.0.0", "author": "系统管理员", "last_editor": "系统管理员", "source": "WhatsApp群组2客服团队聊天记录", "confidence_level": "high", "review_status": "approved", "expiry_date": null}, "relationships": {"related_items": ["price_adjustment_process_001", "third_party_platform_handling_001", "emergency_order_handling_001"], "prerequisites": ["customer_service_basic_training_001"], "follow_ups": ["order_completion_verification_001", "customer_feedback_collection_001"], "supersedes": []}}