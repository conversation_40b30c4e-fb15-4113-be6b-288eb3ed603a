{"processing": {"batch_size": 100, "max_workers": 4, "timeout_seconds": 300, "enable_parallel": true}, "analysis": {"enable_sentiment": true, "enable_entity_extraction": true, "enable_reference_detection": true, "min_confidence": 0.7, "use_gemini_api": true, "fallback_to_rules": true}, "tagging": {"business_types": ["order_processing", "driver_management", "customer_service", "technical_support", "policy_update", "training", "complaint_handling", "quality_control"], "urgency_levels": ["urgent", "important", "normal", "low"], "sentiment_types": ["positive", "negative", "neutral", "complaint", "praise"], "entity_types": ["driver_id", "order_id", "phone_number", "amount", "time_duration", "location", "vehicle_info"]}, "entity_extraction": {"min_confidence": 0.6, "enable_context_analysis": true, "max_context_window": 50, "enable_entity_linking": true}, "network": {"min_edge_weight": 2, "max_nodes": 500, "node_size_factor": 10, "enable_clustering": true, "layout_algorithm": "force_directed", "layout_iterations": 50}, "visualization": {"enable_charts": true, "enable_network": true, "enable_timeline": true, "chart_library": "chartjs", "color_scheme": "default", "max_chart_points": 100}, "output": {"generate_html": true, "generate_json": true, "generate_report": true, "open_browser": false, "save_intermediate": false, "compress_output": false}, "logging": {"level": "INFO", "enable_file_logging": true, "log_rotation": true, "max_log_size_mb": 10}, "performance": {"cache_enabled": true, "cache_size_mb": 100, "memory_limit_mb": 1024, "enable_profiling": false}, "quality_control": {"min_message_length": 5, "max_message_length": 2000, "exclude_system_messages": true, "enable_duplicate_detection": true, "similarity_threshold": 0.8}, "group_mappings": {"group_2": {"name": "客服团队", "knowledge_type": "general", "default_category": "process", "priority": "high", "color": "#3498db"}, "group_7": {"name": "司机问题处理", "knowledge_type": "driver-conduct", "default_category": "violations", "priority": "high", "color": "#e74c3c"}, "group_8": {"name": "各部门信息交流", "knowledge_type": "general", "default_category": "technical", "priority": "medium", "color": "#2ecc71"}}, "file_patterns": {"group_2_patterns": ["*group2*", "*customer*", "*客服*"], "group_7_patterns": ["*group7*", "*driver*", "*司机*"], "group_8_patterns": ["*group8*", "*department*", "*部门*"]}, "html_templates": {"dashboard": {"title": "知识库可视化仪表盘", "theme": "light", "enable_dark_mode": true, "auto_refresh": false}, "network": {"title": "知识网络可视化", "physics_enabled": true, "show_labels": true, "enable_clustering": true}, "trends": {"title": "趋势分析", "time_range_days": 30, "enable_forecasting": false, "show_annotations": true}}, "data_sources": {"whatsapp": {"supported_formats": ["txt", "json", "csv"], "encoding": "utf-8", "date_formats": ["%m/%d/%Y, %I:%M:%S %p", "%d.%m.%Y, %H:%M:%S", "%Y-%m-%d %H:%M:%S"]}}, "export_options": {"knowledge_base": {"format": "json", "include_metadata": true, "validate_schema": true}, "visualization_data": {"format": "json", "compress": false, "include_raw_data": false}, "network_data": {"format": "json", "include_positions": true, "include_metrics": true}, "reports": {"format": "json", "include_statistics": true, "include_errors": true}}}