# 知识库系统 - 传统脚本架构

## 🎯 系统概述

本项目已重构为传统脚本架构，提供独立的Python脚本执行模式和静态HTML可视化方案。系统能够从WhatsApp群组聊天记录中自动提取知识，生成结构化知识库，并提供丰富的可视化分析。

## 🏗️ 架构特点

### ✅ 已移除的组件
- ❌ Flask Web服务器
- ❌ 实时Web API
- ❌ 服务器端依赖

### ✅ 新增的组件
- ✅ 独立Python脚本执行器
- ✅ 静态HTML可视化仪表盘
- ✅ 本地JSON数据文件
- ✅ 客户端JavaScript图表库

## 📁 项目结构

```
knowledge-base/
├── scripts/                         # 核心脚本模块
│   ├── main_processor.py            # 主执行脚本
│   ├── conversation_analyzer.py     # 对话分析器
│   ├── entity_extractor.py          # 实体提取器
│   ├── visualization_generator.py   # 可视化数据生成器
│   └── network_builder.py           # 知识网络构建器
├── static/                          # 静态可视化文件
│   ├── dashboard.html               # 主仪表盘
│   ├── knowledge_network.html       # 知识网络可视化
│   ├── trend_analysis.html          # 趋势分析页面
│   ├── css/dashboard.css            # 样式文件
│   ├── js/dashboard.js              # JavaScript脚本
│   └── data/                        # 生成的JSON数据
├── output/                          # 处理结果输出
│   ├── knowledge_base.json          # 结构化知识库
│   ├── processing_report.json       # 处理报告
│   └── visualization_data/          # 可视化数据
├── config/                          # 配置文件
│   └── script_config.json           # 脚本配置
├── examples/                        # 示例和演示
│   └── script_demo.py               # 使用演示
└── tools/                           # 复用的工具模块
    ├── knowledge_manager.py         # 知识库管理器
    ├── whatsapp_parser.py           # WhatsApp解析器
    └── gemini_integration.py        # Gemini API集成
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 设置Gemini API密钥（可选，用于AI分析）
export GEMINI_API_KEY="your_gemini_api_key"
```

### 2. 基本使用

```bash
# 处理单个文件
python scripts/main_processor.py whatsapp_export.txt

# 处理多个文件
python scripts/main_processor.py group2.txt group7.txt group8.txt

# 使用自定义配置
python scripts/main_processor.py --config config/custom_config.json file.txt

# 处理完成后自动打开浏览器
python scripts/main_processor.py --open-browser file.txt
```

### 3. 查看结果

处理完成后，在浏览器中打开：
- `static/dashboard.html` - 主仪表盘
- `static/knowledge_network.html` - 知识网络图谱
- `static/trend_analysis.html` - 趋势分析

## 📊 功能特性

### 🤖 智能分析功能

#### 多维度标签生成
- **业务类型标签**: 订单处理、司机管理、客服咨询等
- **实体标签**: 司机ID、订单号、金额、时间等
- **情感标签**: 投诉、表扬、中性、紧急等
- **时间标签**: 工作日/周末、时间段分类

#### 实体识别与提取
- **司机ID**: @60124088411 格式的司机标识
- **订单号**: 订单107480 格式的订单标识
- **金额信息**: $25、$30 等价格信息
- **时间信息**: 30分钟、1天 等时长信息
- **位置信息**: 地址和路线信息

#### 引用关系分析
- **@提及关系**: 识别消息中的@提及
- **回复关系**: 检测消息间的回复关系
- **实体关联**: 构建实体间的关联关系
- **话题延续**: 识别对话的话题延续

### 📈 可视化功能

#### 主仪表盘 (dashboard.html)
- **统计卡片**: 总对话数、知识条目、识别实体、今日活动
- **知识库分布**: 通用知识库vs司机操守知识库统计
- **群组活跃度**: 各群组的对话数量对比
- **标签使用频率**: 热门标签的使用统计
- **司机操守统计**: 违规类型、处罚措施分布

#### 知识网络 (knowledge_network.html)
- **实体关联图**: 基于共现关系的实体网络
- **标签关联图**: 标签间的共现和关联关系
- **知识条目网络**: 基于相似性的知识条目关联
- **交互式过滤**: 按节点类型、连接数过滤
- **详细信息**: 点击节点查看详细信息

#### 趋势分析 (trend_analysis.html)
- **时间趋势**: 对话量和知识创建的时间变化
- **每日活跃度**: 24小时活跃度分布
- **每周分布**: 工作日vs周末活动模式
- **处理时间线**: 重要事件的时间序列

### 🔍 数据分析功能

#### 对话分析
- **内容分类**: 自动识别对话的业务类型
- **情感分析**: 基于关键词的情感倾向分析
- **紧急程度**: 识别紧急、重要、一般等级别
- **质量评估**: 对提取内容的质量评分

#### 知识提取
- **问题-解决方案模式**: 识别问题处理流程
- **操作流程模式**: 提取操作步骤和流程
- **违规处理模式**: 司机违规案例和处理措施
- **结构化输出**: 符合JSON Schema的知识条目

## ⚙️ 配置说明

### 脚本配置 (config/script_config.json)

```json
{
  "processing": {
    "batch_size": 100,           // 批处理大小
    "max_workers": 4,            // 最大工作线程数
    "timeout_seconds": 300       // 处理超时时间
  },
  "analysis": {
    "enable_sentiment": true,    // 启用情感分析
    "enable_entity_extraction": true,  // 启用实体提取
    "min_confidence": 0.7        // 最小置信度阈值
  },
  "visualization": {
    "enable_charts": true,       // 启用图表生成
    "enable_network": true,      // 启用网络图
    "chart_library": "chartjs"   // 图表库选择
  }
}
```

### 群组映射

支持通过文件名自动识别或手动指定：

```json
{
  "path/to/customer_chat.txt": "group_2",
  "path/to/driver_chat.txt": "group_7", 
  "path/to/department_chat.txt": "group_8"
}
```

## 📤 输出文件

### 结构化知识库
- **knowledge_base.json**: 符合现有schema的完整知识库
- **processing_report.json**: 详细的处理统计报告

### 可视化数据
- **dashboard_stats.json**: 仪表盘统计数据
- **knowledge_distribution.json**: 知识分布数据
- **time_trends.json**: 时间趋势数据
- **tag_analysis.json**: 标签分析数据
- **entity_statistics.json**: 实体统计数据
- **network.json**: 网络图数据

### HTML界面
- **dashboard.html**: 交互式主仪表盘
- **knowledge_network.html**: 知识网络可视化
- **trend_analysis.html**: 趋势分析界面

## 🔧 高级功能

### 命令行参数

```bash
python scripts/main_processor.py [选项] 文件列表

选项:
  --config, -c          指定配置文件路径
  --group-mapping, -g   指定群组映射文件
  --output-dir, -o      指定输出目录
  --verbose, -v         详细输出模式
  --open-browser        处理完成后打开浏览器
```

### 批量处理

```python
# Python脚本中使用
from scripts.main_processor import KnowledgeBaseProcessor

processor = KnowledgeBaseProcessor()
result = processor.process_whatsapp_files(
    file_paths=['file1.txt', 'file2.txt'],
    group_mappings={'file1.txt': 'group_2', 'file2.txt': 'group_7'}
)
```

## 📋 使用示例

### 示例1: 基本处理
```bash
# 处理客服团队聊天记录
python scripts/main_processor.py customer_service_chat.txt
```

### 示例2: 多群组处理
```bash
# 同时处理多个群组
python scripts/main_processor.py \
  --group-mapping group_mapping.json \
  group2_customer.txt \
  group7_driver.txt \
  group8_department.txt
```

### 示例3: 自定义配置
```bash
# 使用自定义配置
python scripts/main_processor.py \
  --config config/production_config.json \
  --verbose \
  --open-browser \
  whatsapp_export.txt
```

## 🔍 故障排除

### 常见问题

1. **Gemini API错误**
   ```
   解决: 检查GEMINI_API_KEY环境变量是否正确设置
   ```

2. **文件解析失败**
   ```
   解决: 确保WhatsApp导出文件格式正确，支持TXT/JSON/CSV格式
   ```

3. **可视化页面空白**
   ```
   解决: 检查static/data/目录下是否有JSON数据文件
   ```

4. **处理速度慢**
   ```
   解决: 调整config中的batch_size和max_workers参数
   ```

## 🚀 部署建议

### 开发环境
```bash
git clone <repository>
cd knowledge-base
pip install -r requirements.txt
python examples/script_demo.py
```

### 生产环境
```bash
# 设置环境变量
export GEMINI_API_KEY="your_key"
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 运行处理脚本
python scripts/main_processor.py production_data.txt

# 部署静态文件到Web服务器
cp -r static/* /var/www/html/knowledge-dashboard/
```

## 📞 技术支持

如有问题或建议，请：
1. 查看 `examples/script_demo.py` 了解使用方法
2. 检查 `output/processing_*.log` 日志文件
3. 参考配置文件 `config/script_config.json`

---

*文档版本: 2.0.0*  
*架构类型: 传统脚本架构*  
*最后更新: 2025-07-09*
