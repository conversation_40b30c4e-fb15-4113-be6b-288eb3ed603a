#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库主处理脚本
传统脚本架构的核心执行器，负责整个知识提取和可视化流程
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent.parent / "tools"))

from conversation_analyzer import ConversationAnalyzer
from entity_extractor import EntityExtractor
from visualization_generator import VisualizationGenerator
from network_builder import NetworkBuilder
from knowledge_manager import KnowledgeManager


class KnowledgeBaseProcessor:
    """知识库主处理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化处理器
        
        Args:
            config_path: 配置文件路径
        """
        self.base_path = Path(__file__).parent.parent
        self.config_path = config_path or self.base_path / "config" / "script_config.json"
        self.output_path = self.base_path / "output"
        self.static_path = self.base_path / "static"
        
        # 创建输出目录
        self.output_path.mkdir(exist_ok=True)
        (self.output_path / "visualization_data").mkdir(exist_ok=True)
        (self.static_path / "data").mkdir(exist_ok=True)
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化组件
        self.conversation_analyzer = ConversationAnalyzer(self.config)
        self.entity_extractor = EntityExtractor(self.config)
        self.visualization_generator = VisualizationGenerator(self.config)
        self.network_builder = NetworkBuilder(self.config)
        self.knowledge_manager = KnowledgeManager()
        
        # 配置日志
        self._setup_logging()
        
        # 处理统计
        self.processing_stats = {
            'start_time': None,
            'end_time': None,
            'total_messages': 0,
            'processed_messages': 0,
            'extracted_knowledge': 0,
            'generated_tags': 0,
            'identified_entities': 0,
            'network_nodes': 0,
            'network_edges': 0,
            'errors': []
        }
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # 创建默认配置
            default_config = {
                "processing": {
                    "batch_size": 100,
                    "max_workers": 4,
                    "timeout_seconds": 300
                },
                "analysis": {
                    "enable_sentiment": True,
                    "enable_entity_extraction": True,
                    "enable_reference_detection": True,
                    "min_confidence": 0.7
                },
                "tagging": {
                    "business_types": [
                        "order_processing", "driver_management", "customer_service",
                        "technical_support", "policy_update", "training"
                    ],
                    "urgency_levels": ["urgent", "important", "normal", "low"],
                    "sentiment_types": ["positive", "negative", "neutral", "complaint", "praise"]
                },
                "visualization": {
                    "enable_charts": True,
                    "enable_network": True,
                    "enable_timeline": True,
                    "chart_library": "chartjs"
                },
                "output": {
                    "generate_html": True,
                    "generate_json": True,
                    "generate_report": True,
                    "open_browser": False
                }
            }
            
            # 保存默认配置
            self.config_path.parent.mkdir(exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            
            return default_config
    
    def _setup_logging(self):
        """配置日志系统"""
        log_level = self.config.get('logging', {}).get('level', 'INFO')
        log_file = self.output_path / f"processing_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('KnowledgeBaseProcessor')
    
    def process_whatsapp_files(self, file_paths: List[str], group_mappings: Dict[str, str] = None) -> Dict[str, Any]:
        """
        处理WhatsApp文件列表
        
        Args:
            file_paths: 文件路径列表
            group_mappings: 文件到群组的映射
            
        Returns:
            Dict: 处理结果
        """
        self.processing_stats['start_time'] = datetime.now()
        self.logger.info(f"开始处理 {len(file_paths)} 个WhatsApp文件")
        
        all_conversations = []
        all_entities = []
        all_knowledge_items = []
        
        try:
            # 阶段1: 数据输入和分析
            self.logger.info("阶段1: 数据输入和对话分析")
            for file_path in file_paths:
                self.logger.info(f"处理文件: {file_path}")
                
                # 确定群组类型
                group_id = self._determine_group_id(file_path, group_mappings)
                
                # 分析对话
                conversations = self.conversation_analyzer.analyze_file(file_path, group_id)
                all_conversations.extend(conversations)
                
                self.processing_stats['total_messages'] += len(conversations)
                self.logger.info(f"从 {file_path} 分析出 {len(conversations)} 条对话")
            
            # 阶段2: 实体提取和标签生成
            self.logger.info("阶段2: 实体提取和标签生成")
            for conversation in all_conversations:
                # 提取实体
                entities = self.entity_extractor.extract_entities(conversation)
                all_entities.extend(entities)
                
                # 生成多维度标签
                tags = self.conversation_analyzer.generate_tags(conversation)
                conversation['generated_tags'] = tags
                
                self.processing_stats['processed_messages'] += 1
            
            self.processing_stats['identified_entities'] = len(all_entities)
            self.processing_stats['generated_tags'] = sum(
                len(conv.get('generated_tags', [])) for conv in all_conversations
            )
            
            # 阶段3: 知识提取
            self.logger.info("阶段3: 知识提取和结构化")
            knowledge_items = self.conversation_analyzer.extract_knowledge_items(all_conversations)
            all_knowledge_items.extend(knowledge_items)
            
            self.processing_stats['extracted_knowledge'] = len(all_knowledge_items)
            
            # 阶段4: 网络构建
            self.logger.info("阶段4: 知识网络构建")
            network_data = self.network_builder.build_network(
                all_conversations, all_entities, all_knowledge_items
            )
            
            self.processing_stats['network_nodes'] = len(network_data.get('nodes', []))
            self.processing_stats['network_edges'] = len(network_data.get('edges', []))
            
            # 阶段5: 输出生成
            self.logger.info("阶段5: 输出生成")
            output_files = self._generate_outputs(
                all_conversations, all_entities, all_knowledge_items, network_data
            )
            
            self.processing_stats['end_time'] = datetime.now()
            
            # 生成处理报告
            report = self._generate_processing_report()
            
            self.logger.info("处理完成！")
            return {
                'success': True,
                'statistics': self.processing_stats,
                'output_files': output_files,
                'report': report
            }
            
        except Exception as e:
            self.processing_stats['errors'].append(str(e))
            self.logger.error(f"处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'statistics': self.processing_stats
            }
    
    def _determine_group_id(self, file_path: str, group_mappings: Dict[str, str] = None) -> str:
        """确定文件对应的群组ID"""
        if group_mappings and file_path in group_mappings:
            return group_mappings[file_path]
        
        # 基于文件名推断
        filename = Path(file_path).name.lower()
        if 'group2' in filename or 'customer' in filename or '客服' in filename:
            return 'group_2'
        elif 'group7' in filename or 'driver' in filename or '司机' in filename:
            return 'group_7'
        elif 'group8' in filename or 'department' in filename or '部门' in filename:
            return 'group_8'
        else:
            return 'unknown'
    
    def _generate_outputs(self, conversations: List[Dict], entities: List[Dict], 
                         knowledge_items: List[Dict], network_data: Dict) -> Dict[str, str]:
        """生成所有输出文件"""
        output_files = {}
        
        # 1. 生成结构化知识库JSON
        knowledge_base_file = self.output_path / "knowledge_base.json"
        with open(knowledge_base_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'total_items': len(knowledge_items),
                    'version': '1.0.0'
                },
                'knowledge_items': knowledge_items
            }, f, ensure_ascii=False, indent=2)
        output_files['knowledge_base'] = str(knowledge_base_file)
        
        # 2. 生成可视化数据
        if self.config.get('visualization', {}).get('enable_charts', True):
            viz_data = self.visualization_generator.generate_visualization_data(
                conversations, entities, knowledge_items
            )
            
            # 保存到static/data目录供HTML使用
            for data_type, data in viz_data.items():
                data_file = self.static_path / "data" / f"{data_type}.json"
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                output_files[f'viz_{data_type}'] = str(data_file)
        
        # 3. 生成网络数据
        if self.config.get('visualization', {}).get('enable_network', True):
            network_file = self.static_path / "data" / "network.json"
            with open(network_file, 'w', encoding='utf-8') as f:
                json.dump(network_data, f, ensure_ascii=False, indent=2)
            output_files['network'] = str(network_file)
        
        # 4. 生成HTML文件
        if self.config.get('output', {}).get('generate_html', True):
            html_files = self.visualization_generator.generate_html_files()
            output_files.update(html_files)
        
        return output_files
    
    def _generate_processing_report(self) -> Dict[str, Any]:
        """生成处理报告"""
        duration = None
        if self.processing_stats['start_time'] and self.processing_stats['end_time']:
            duration = (self.processing_stats['end_time'] - self.processing_stats['start_time']).total_seconds()
        
        report = {
            'processing_summary': {
                'start_time': self.processing_stats['start_time'].isoformat() if self.processing_stats['start_time'] else None,
                'end_time': self.processing_stats['end_time'].isoformat() if self.processing_stats['end_time'] else None,
                'duration_seconds': duration,
                'success': len(self.processing_stats['errors']) == 0
            },
            'data_statistics': {
                'total_messages': self.processing_stats['total_messages'],
                'processed_messages': self.processing_stats['processed_messages'],
                'extracted_knowledge': self.processing_stats['extracted_knowledge'],
                'generated_tags': self.processing_stats['generated_tags'],
                'identified_entities': self.processing_stats['identified_entities'],
                'network_nodes': self.processing_stats['network_nodes'],
                'network_edges': self.processing_stats['network_edges']
            },
            'quality_metrics': {
                'processing_rate': self.processing_stats['processed_messages'] / max(self.processing_stats['total_messages'], 1),
                'knowledge_extraction_rate': self.processing_stats['extracted_knowledge'] / max(self.processing_stats['processed_messages'], 1),
                'average_tags_per_message': self.processing_stats['generated_tags'] / max(self.processing_stats['processed_messages'], 1)
            },
            'errors': self.processing_stats['errors']
        }
        
        # 保存报告
        report_file = self.output_path / "processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report


def main():
    """主函数 - 命令行入口"""
    parser = argparse.ArgumentParser(description='知识库处理脚本')
    parser.add_argument('files', nargs='+', help='WhatsApp导出文件路径')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--group-mapping', '-g', help='群组映射JSON文件')
    parser.add_argument('--output-dir', '-o', help='输出目录')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--open-browser', action='store_true', help='处理完成后打开浏览器')
    
    args = parser.parse_args()
    
    # 初始化处理器
    processor = KnowledgeBaseProcessor(args.config)
    
    # 加载群组映射
    group_mappings = {}
    if args.group_mapping:
        try:
            with open(args.group_mapping, 'r', encoding='utf-8') as f:
                group_mappings = json.load(f)
        except Exception as e:
            print(f"警告: 无法加载群组映射文件: {e}")
    
    # 处理文件
    print(f"开始处理 {len(args.files)} 个文件...")
    result = processor.process_whatsapp_files(args.files, group_mappings)
    
    if result['success']:
        print("✅ 处理成功完成！")
        print(f"📊 统计信息:")
        stats = result['statistics']
        print(f"  - 总消息数: {stats['total_messages']}")
        print(f"  - 处理消息数: {stats['processed_messages']}")
        print(f"  - 提取知识条目: {stats['extracted_knowledge']}")
        print(f"  - 生成标签数: {stats['generated_tags']}")
        print(f"  - 识别实体数: {stats['identified_entities']}")
        
        print(f"\n📁 输出文件:")
        for file_type, file_path in result['output_files'].items():
            print(f"  - {file_type}: {file_path}")
        
        # 打开浏览器
        if args.open_browser:
            import webbrowser
            dashboard_path = processor.static_path / "dashboard.html"
            if dashboard_path.exists():
                webbrowser.open(f"file://{dashboard_path.absolute()}")
    else:
        print("❌ 处理失败！")
        print(f"错误: {result['error']}")
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
