#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库搜索引擎模块
提供全文搜索、标签过滤、分类浏览等功能
"""

import os
import json
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import logging


class KnowledgeSearchEngine:
    """知识库搜索引擎"""
    
    def __init__(self, data_path: Path, settings: Dict):
        """
        初始化搜索引擎
        
        Args:
            data_path: 数据目录路径
            settings: 系统设置
        """
        self.data_path = data_path
        self.settings = settings
        self.search_config = settings.get('search', {})
        
        # 搜索配置
        self.default_limit = self.search_config.get('default_results_limit', 20)
        self.max_limit = self.search_config.get('max_results_limit', 100)
        self.fuzzy_enabled = self.search_config.get('enable_fuzzy_search', True)
        self.fuzzy_threshold = self.search_config.get('fuzzy_threshold', 0.8)
        self.search_fields = self.search_config.get('search_fields', [
            'title', 'description', 'content.summary', 'content.details', 'tags'
        ])
        
        # 缓存
        self._cache = {}
        self._cache_timestamp = None
        
    def _load_all_items(self, force_reload: bool = False) -> List[Dict]:
        """
        加载所有知识条目
        
        Args:
            force_reload: 是否强制重新加载
            
        Returns:
            List[Dict]: 所有知识条目列表
        """
        # 检查缓存
        if not force_reload and self._cache and self._cache_timestamp:
            cache_age = (datetime.now() - self._cache_timestamp).seconds
            cache_ttl = self.settings.get('performance', {}).get('cache_ttl_minutes', 60) * 60
            if cache_age < cache_ttl:
                return self._cache.get('items', [])
        
        items = []
        
        # 遍历所有JSON文件
        for root, dirs, files in os.walk(self.data_path):
            for file in files:
                if file.endswith('.json'):
                    file_path = Path(root) / file
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            items.append(data)
                    except Exception as e:
                        logging.error(f"加载文件失败 {file_path}: {e}")
        
        # 更新缓存
        self._cache = {'items': items}
        self._cache_timestamp = datetime.now()
        
        return items
    
    def _extract_field_value(self, item: Dict, field_path: str) -> str:
        """
        从条目中提取字段值
        
        Args:
            item: 知识条目
            field_path: 字段路径（支持点号分隔的嵌套路径）
            
        Returns:
            str: 字段值
        """
        try:
            value = item
            for key in field_path.split('.'):
                if isinstance(value, dict):
                    value = value.get(key, '')
                else:
                    return ''
            
            if isinstance(value, list):
                return ' '.join(str(v) for v in value)
            return str(value) if value else ''
            
        except Exception:
            return ''
    
    def _calculate_relevance_score(self, item: Dict, query: str) -> float:
        """
        计算条目与查询的相关性得分
        
        Args:
            item: 知识条目
            query: 搜索查询
            
        Returns:
            float: 相关性得分 (0-1)
        """
        if not query:
            return 0.0
        
        query_lower = query.lower()
        total_score = 0.0
        field_weights = {
            'title': 3.0,
            'description': 2.0,
            'content.summary': 2.0,
            'content.details': 1.0,
            'tags': 2.5
        }
        
        for field in self.search_fields:
            field_value = self._extract_field_value(item, field).lower()
            if not field_value:
                continue
            
            weight = field_weights.get(field, 1.0)
            
            # 精确匹配得分最高
            if query_lower in field_value:
                if query_lower == field_value:
                    score = 1.0  # 完全匹配
                elif field_value.startswith(query_lower):
                    score = 0.9  # 前缀匹配
                else:
                    score = 0.7  # 包含匹配
                
                total_score += score * weight
            
            # 模糊匹配
            elif self.fuzzy_enabled:
                fuzzy_score = self._fuzzy_match(query_lower, field_value)
                if fuzzy_score >= self.fuzzy_threshold:
                    total_score += fuzzy_score * weight * 0.5
        
        # 标准化得分
        max_possible_score = sum(field_weights.values())
        return min(total_score / max_possible_score, 1.0)
    
    def _fuzzy_match(self, query: str, text: str) -> float:
        """
        模糊匹配算法
        
        Args:
            query: 查询字符串
            text: 目标文本
            
        Returns:
            float: 匹配得分 (0-1)
        """
        # 简单的编辑距离算法
        if not query or not text:
            return 0.0
        
        # 将查询分词
        query_words = query.split()
        matched_words = 0
        
        for word in query_words:
            if word in text:
                matched_words += 1
        
        return matched_words / len(query_words) if query_words else 0.0
    
    def _apply_filters(self, items: List[Dict], **filters) -> List[Dict]:
        """
        应用过滤条件
        
        Args:
            items: 知识条目列表
            **filters: 过滤条件
            
        Returns:
            List[Dict]: 过滤后的条目列表
        """
        filtered_items = items
        
        # 分类过滤
        if 'category' in filters:
            category = filters['category']
            filtered_items = [item for item in filtered_items 
                            if item.get('category') == category]
        
        # 子分类过滤
        if 'subcategory' in filters:
            subcategory = filters['subcategory']
            filtered_items = [item for item in filtered_items 
                            if item.get('subcategory') == subcategory]
        
        # 标签过滤
        if 'tags' in filters:
            required_tags = filters['tags']
            if isinstance(required_tags, str):
                required_tags = [required_tags]
            
            filtered_items = [item for item in filtered_items 
                            if any(tag in item.get('tags', []) for tag in required_tags)]
        
        # 时间范围过滤
        if 'date_range' in filters:
            date_range = filters['date_range']
            start_date = date_range.get('start')
            end_date = date_range.get('end')
            
            if start_date or end_date:
                filtered_items = [item for item in filtered_items 
                                if self._check_date_range(item, start_date, end_date)]
        
        # 可信度过滤
        if 'confidence_level' in filters:
            confidence = filters['confidence_level']
            filtered_items = [item for item in filtered_items 
                            if item.get('metadata', {}).get('confidence_level') == confidence]
        
        # 审核状态过滤
        if 'review_status' in filters:
            status = filters['review_status']
            filtered_items = [item for item in filtered_items 
                            if item.get('metadata', {}).get('review_status') == status]
        
        return filtered_items
    
    def _check_date_range(self, item: Dict, start_date: str = None, end_date: str = None) -> bool:
        """
        检查条目是否在指定日期范围内
        
        Args:
            item: 知识条目
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            bool: 是否在范围内
        """
        try:
            item_date = item.get('metadata', {}).get('updated_at', '')
            if not item_date:
                return True
            
            item_datetime = datetime.fromisoformat(item_date.replace('Z', '+00:00'))
            
            if start_date:
                start_datetime = datetime.fromisoformat(start_date)
                if item_datetime < start_datetime:
                    return False
            
            if end_date:
                end_datetime = datetime.fromisoformat(end_date)
                if item_datetime > end_datetime:
                    return False
            
            return True
            
        except Exception:
            return True
    
    def search(self, query: str = '', limit: int = None, **filters) -> List[Dict]:
        """
        执行搜索
        
        Args:
            query: 搜索查询字符串
            limit: 结果数量限制
            **filters: 过滤条件
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        # 设置结果限制
        if limit is None:
            limit = self.default_limit
        limit = min(limit, self.max_limit)
        
        # 加载所有条目
        all_items = self._load_all_items()
        
        # 应用过滤条件
        filtered_items = self._apply_filters(all_items, **filters)
        
        # 如果没有查询字符串，返回过滤后的结果
        if not query:
            return filtered_items[:limit]
        
        # 计算相关性得分并排序
        scored_items = []
        for item in filtered_items:
            score = self._calculate_relevance_score(item, query)
            if score > 0:
                scored_items.append((item, score))
        
        # 按得分排序
        scored_items.sort(key=lambda x: x[1], reverse=True)
        
        # 返回结果
        return [item for item, score in scored_items[:limit]]
    
    def get_related_items(self, item_id: str, limit: int = 5) -> List[Dict]:
        """
        获取相关条目
        
        Args:
            item_id: 条目ID
            limit: 结果数量限制
            
        Returns:
            List[Dict]: 相关条目列表
        """
        all_items = self._load_all_items()
        
        # 找到目标条目
        target_item = None
        for item in all_items:
            if item.get('id') == item_id:
                target_item = item
                break
        
        if not target_item:
            return []
        
        # 获取显式关联的条目
        related_ids = target_item.get('relationships', {}).get('related_items', [])
        related_items = []
        
        for item in all_items:
            if item.get('id') in related_ids:
                related_items.append(item)
        
        # 如果显式关联不足，基于标签和分类推荐
        if len(related_items) < limit:
            target_tags = set(target_item.get('tags', []))
            target_category = target_item.get('category')
            
            for item in all_items:
                if item.get('id') == item_id or item.get('id') in related_ids:
                    continue
                
                # 计算相似度
                item_tags = set(item.get('tags', []))
                tag_similarity = len(target_tags & item_tags) / len(target_tags | item_tags) if target_tags | item_tags else 0
                category_match = 1.0 if item.get('category') == target_category else 0.0
                
                similarity = (tag_similarity * 0.7) + (category_match * 0.3)
                
                if similarity > 0.3:  # 相似度阈值
                    related_items.append(item)
                
                if len(related_items) >= limit:
                    break
        
        return related_items[:limit]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        
        Returns:
            Dict: 统计信息
        """
        all_items = self._load_all_items()
        
        stats = {
            'total_items': len(all_items),
            'categories': {},
            'tags': {},
            'recent_updates': []
        }
        
        # 分类统计
        for item in all_items:
            category = item.get('category', 'unknown')
            stats['categories'][category] = stats['categories'].get(category, 0) + 1
        
        # 标签统计
        for item in all_items:
            for tag in item.get('tags', []):
                stats['tags'][tag] = stats['tags'].get(tag, 0) + 1
        
        # 最近更新
        sorted_items = sorted(all_items, 
                            key=lambda x: x.get('metadata', {}).get('updated_at', ''), 
                            reverse=True)
        stats['recent_updates'] = [
            {
                'id': item.get('id'),
                'title': item.get('title'),
                'updated_at': item.get('metadata', {}).get('updated_at')
            }
            for item in sorted_items[:10]
        ]
        
        return stats
