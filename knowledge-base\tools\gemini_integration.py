#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Gemini API集成模块
提供智能内容分析、分类和知识提取功能
"""

import json
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import hashlib
import re

import google.generativeai as genai


class GeminiKnowledgeExtractor:
    """基于Gemini API的知识提取器"""
    
    def __init__(self, api_key: str, config: Dict):
        """
        初始化Gemini知识提取器
        
        Args:
            api_key: Google Gemini API密钥
            config: 配置参数
        """
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        self.config = config
        self.logger = logging.getLogger('GeminiKnowledgeExtractor')
        
        # API调用统计
        self.api_calls_count = 0
        self.api_calls_hour = datetime.now().hour
        
        # 提示词模板
        self.prompts = self._load_prompts()
    
    def _load_prompts(self) -> Dict[str, str]:
        """加载提示词模板"""
        return {
            "content_classifier": """
你是一个专业的内容分析师，需要分析WhatsApp群组聊天内容并进行分类。

请分析以下聊天内容，并判断：
1. 这条消息是否包含有价值的业务知识
2. 如果包含，属于哪个知识库类型
3. 具体的分类和子分类
4. 置信度评分

聊天内容：
{content}

群组信息：{group_info}

请以JSON格式返回分析结果：
{{
    "has_knowledge": true/false,
    "knowledge_type": "general" 或 "driver-conduct",
    "category": "technical/process/faq/violations/regulations/penalties",
    "subcategory": "具体子分类",
    "confidence_score": 0.0-1.0,
    "reasoning": "分析理由"
}}
""",
            
            "knowledge_extractor": """
你是一个专业的知识管理专家，需要从WhatsApp聊天内容中提取结构化的知识信息。

聊天内容：
{content}

分类信息：{classification}

请提取以下信息并以JSON格式返回：
{{
    "id": "自动生成的唯一ID",
    "title": "简洁明确的标题",
    "description": "详细描述",
    "category": "{category}",
    "subcategory": "{subcategory}",
    "tags": ["相关标签列表"],
    "content": {{
        "summary": "内容摘要",
        "details": "详细内容",
        "examples": ["示例列表"],
        "steps": [
            {{
                "step": 1,
                "description": "步骤描述",
                "notes": "注意事项"
            }}
        ]
    }},
    "metadata": {{
        "created_at": "{timestamp}",
        "updated_at": "{timestamp}",
        "version": "1.0.0",
        "author": "自动提取系统",
        "source": "whatsapp_{group_id}",
        "confidence_level": "high/medium/low",
        "review_status": "pending",
        "extraction_method": "gemini_api"
    }},
    "relationships": {{
        "related_items": [],
        "prerequisites": [],
        "follow_ups": []
    }}
}}

注意：
1. ID格式：{category}_{topic}_{timestamp}
2. 标签使用小写字母和下划线
3. 确保内容结构化和完整
4. 如果是司机操守相关，需要包含driver_conduct_specific字段
""",
            
            "driver_conduct_extractor": """
你是司机管理专家，需要从聊天内容中提取司机操守相关的结构化信息。

聊天内容：
{content}

除了基本的知识结构外，还需要提取以下司机操守特定信息：
{{
    "driver_conduct_specific": {{
        "violation_type": "tardiness/no_show/service_attitude/capability_mismatch/safety_violation",
        "severity_level": "minor/moderate/serious/critical",
        "penalty_type": "warning/financial_penalty/service_suspension/permanent_ban",
        "penalty_details": {{
            "financial_amount": 数字或null,
            "suspension_days": 数字或null,
            "additional_requirements": ["要求列表"]
        }},
        "evidence_types": ["screenshot/audio_recording/customer_complaint/system_record"],
        "case_studies": [
            {{
                "case_id": "案例ID",
                "driver_info": "司机信息（脱敏）",
                "incident_description": "事件描述",
                "resolution": "处理结果",
                "outcome": "最终结果"
            }}
        ]
    }}
}}

请确保敏感信息（如电话号码、真实姓名）被适当脱敏。
""",
            
            "quality_assessor": """
你是质量评估专家，需要评估提取的知识内容的质量。

知识内容：
{content}

请从以下维度评估质量并给出0-1的评分：
1. 内容完整性：信息是否完整、结构是否清晰
2. 逻辑一致性：内容是否逻辑清晰、前后一致
3. 实用性：对业务是否有实际价值
4. 准确性：信息是否准确可靠

返回JSON格式：
{{
    "completeness_score": 0.0-1.0,
    "consistency_score": 0.0-1.0,
    "usefulness_score": 0.0-1.0,
    "accuracy_score": 0.0-1.0,
    "overall_score": 0.0-1.0,
    "quality_issues": ["发现的问题列表"],
    "improvement_suggestions": ["改进建议列表"]
}}
""",
            
            "duplicate_detector": """
你是重复内容检测专家，需要判断新内容是否与现有知识库内容重复。

新内容：
{new_content}

现有相似内容：
{existing_content}

请分析是否重复并返回JSON格式：
{{
    "is_duplicate": true/false,
    "similarity_score": 0.0-1.0,
    "duplicate_type": "exact/semantic/partial/none",
    "reasoning": "判断理由",
    "merge_suggestion": "如果相似，建议如何合并"
}}
"""
        }
    
    async def classify_content(self, content: str, group_info: str) -> Optional[Dict]:
        """
        分类聊天内容
        
        Args:
            content: 聊天内容
            group_info: 群组信息
            
        Returns:
            Dict: 分类结果
        """
        try:
            # 检查API调用限制
            if not self._check_api_limits():
                self.logger.warning("API调用限制，跳过处理")
                return None
            
            prompt = self.prompts["content_classifier"].format(
                content=content,
                group_info=group_info
            )
            
            response = await self._call_gemini_api(prompt)
            if response:
                return self._parse_json_response(response)
            
        except Exception as e:
            self.logger.error(f"内容分类失败: {e}")
        
        return None
    
    async def extract_knowledge(self, content: str, classification: Dict, group_id: str) -> Optional[Dict]:
        """
        提取结构化知识
        
        Args:
            content: 聊天内容
            classification: 分类结果
            group_id: 群组ID
            
        Returns:
            Dict: 提取的知识结构
        """
        try:
            if not self._check_api_limits():
                return None
            
            timestamp = datetime.now().isoformat()
            category = classification.get('category', 'unknown')
            subcategory = classification.get('subcategory', 'unknown')
            
            # 选择合适的提示词
            if classification.get('knowledge_type') == 'driver-conduct':
                prompt_template = self.prompts["driver_conduct_extractor"]
            else:
                prompt_template = self.prompts["knowledge_extractor"]
            
            prompt = prompt_template.format(
                content=content,
                classification=json.dumps(classification, ensure_ascii=False),
                category=category,
                subcategory=subcategory,
                timestamp=timestamp,
                group_id=group_id
            )
            
            response = await self._call_gemini_api(prompt)
            if response:
                knowledge_item = self._parse_json_response(response)
                if knowledge_item:
                    # 添加置信度分数
                    knowledge_item['confidence_score'] = classification.get('confidence_score', 0.5)
                    return knowledge_item
            
        except Exception as e:
            self.logger.error(f"知识提取失败: {e}")
        
        return None
    
    async def assess_quality(self, content: Dict) -> Optional[Dict]:
        """
        评估内容质量
        
        Args:
            content: 知识内容
            
        Returns:
            Dict: 质量评估结果
        """
        try:
            if not self._check_api_limits():
                return None
            
            prompt = self.prompts["quality_assessor"].format(
                content=json.dumps(content, ensure_ascii=False, indent=2)
            )
            
            response = await self._call_gemini_api(prompt)
            if response:
                return self._parse_json_response(response)
            
        except Exception as e:
            self.logger.error(f"质量评估失败: {e}")
        
        return None
    
    async def detect_duplicate(self, new_content: Dict, existing_content: List[Dict]) -> Optional[Dict]:
        """
        检测重复内容
        
        Args:
            new_content: 新内容
            existing_content: 现有内容列表
            
        Returns:
            Dict: 重复检测结果
        """
        try:
            if not existing_content or not self._check_api_limits():
                return {"is_duplicate": False, "similarity_score": 0.0}
            
            # 找到最相似的内容
            most_similar = self._find_most_similar(new_content, existing_content)
            
            if most_similar:
                prompt = self.prompts["duplicate_detector"].format(
                    new_content=json.dumps(new_content, ensure_ascii=False, indent=2),
                    existing_content=json.dumps(most_similar, ensure_ascii=False, indent=2)
                )
                
                response = await self._call_gemini_api(prompt)
                if response:
                    return self._parse_json_response(response)
            
        except Exception as e:
            self.logger.error(f"重复检测失败: {e}")
        
        return {"is_duplicate": False, "similarity_score": 0.0}
    
    def _find_most_similar(self, new_content: Dict, existing_content: List[Dict]) -> Optional[Dict]:
        """找到最相似的现有内容"""
        new_text = f"{new_content.get('title', '')} {new_content.get('description', '')}"
        max_similarity = 0
        most_similar = None
        
        for existing in existing_content:
            existing_text = f"{existing.get('title', '')} {existing.get('description', '')}"
            similarity = self._calculate_text_similarity(new_text, existing_text)
            
            if similarity > max_similarity:
                max_similarity = similarity
                most_similar = existing
        
        # 只有相似度超过阈值才返回
        return most_similar if max_similarity > 0.3 else None
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    async def _call_gemini_api(self, prompt: str) -> Optional[str]:
        """调用Gemini API"""
        try:
            self.api_calls_count += 1
            
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,  # 降低随机性，提高一致性
                    max_output_tokens=2048,
                    top_p=0.8,
                    top_k=40
                )
            )
            
            if response and response.text:
                return response.text.strip()
            
        except Exception as e:
            self.logger.error(f"Gemini API调用失败: {e}")
        
        return None
    
    def _parse_json_response(self, response: str) -> Optional[Dict]:
        """解析JSON响应"""
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，尝试直接解析
                return json.loads(response)
                
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            self.logger.debug(f"原始响应: {response}")
        
        return None
    
    def _check_api_limits(self) -> bool:
        """检查API调用限制"""
        current_hour = datetime.now().hour
        
        # 如果是新的小时，重置计数
        if current_hour != self.api_calls_hour:
            self.api_calls_count = 0
            self.api_calls_hour = current_hour
        
        max_calls = self.config.get("processing_limits", {}).get("max_api_calls_per_hour", 1000)
        return self.api_calls_count < max_calls
    
    def get_api_usage_stats(self) -> Dict[str, Any]:
        """获取API使用统计"""
        return {
            "current_hour": self.api_calls_hour,
            "calls_this_hour": self.api_calls_count,
            "max_calls_per_hour": self.config.get("processing_limits", {}).get("max_api_calls_per_hour", 1000)
        }


class ContentPreprocessor:
    """内容预处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger('ContentPreprocessor')
    
    def process_messages(self, messages: List[Dict], filters: Dict) -> List[Dict]:
        """
        预处理消息列表
        
        Args:
            messages: 原始消息列表
            filters: 过滤配置
            
        Returns:
            List[Dict]: 处理后的消息列表
        """
        processed = []
        
        for message in messages:
            # 应用过滤器
            if self._should_process_message(message, filters):
                # 清洗内容
                cleaned_message = self._clean_message(message)
                if cleaned_message:
                    processed.append(cleaned_message)
        
        # 合并连续消息
        merged = self._merge_consecutive_messages(processed)
        
        self.logger.info(f"预处理完成: {len(messages)} -> {len(merged)}")
        return merged
    
    def _should_process_message(self, message: Dict, filters: Dict) -> bool:
        """判断是否应该处理消息"""
        content = message.get('content', '')
        
        # 长度过滤
        min_length = filters.get('min_message_length', 10)
        max_length = filters.get('max_message_length', 2000)
        
        if len(content) < min_length or len(content) > max_length:
            return False
        
        # 排除模式过滤
        exclude_patterns = filters.get('exclude_patterns', [])
        for pattern in exclude_patterns:
            if pattern in content:
                return False
        
        # 排除纯表情符号或特殊字符
        if re.match(r'^[\s\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]+$', content):
            return False
        
        return True
    
    def _clean_message(self, message: Dict) -> Optional[Dict]:
        """清洗单条消息"""
        content = message.get('content', '')
        
        # 移除多余的空白字符
        content = re.sub(r'\s+', ' ', content).strip()
        
        # 脱敏处理
        content = self._anonymize_sensitive_info(content)
        
        if not content:
            return None
        
        cleaned_message = message.copy()
        cleaned_message['content'] = content
        cleaned_message['processed_at'] = datetime.now().isoformat()
        
        return cleaned_message
    
    def _anonymize_sensitive_info(self, content: str) -> str:
        """脱敏敏感信息"""
        # 脱敏电话号码
        content = re.sub(r'\+?\d{10,15}', '[电话号码]', content)
        
        # 脱敏邮箱地址
        content = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '[邮箱地址]', content)
        
        # 脱敏身份证号码
        content = re.sub(r'\b\d{15}|\d{18}\b', '[身份证号]', content)
        
        return content
    
    def _merge_consecutive_messages(self, messages: List[Dict]) -> List[Dict]:
        """合并连续的相关消息"""
        if not messages:
            return []
        
        merged = []
        current_group = [messages[0]]
        
        for i in range(1, len(messages)):
            current_msg = messages[i]
            prev_msg = messages[i-1]
            
            # 判断是否应该合并
            if self._should_merge_messages(prev_msg, current_msg):
                current_group.append(current_msg)
            else:
                # 合并当前组并开始新组
                merged.append(self._merge_message_group(current_group))
                current_group = [current_msg]
        
        # 处理最后一组
        if current_group:
            merged.append(self._merge_message_group(current_group))
        
        return merged
    
    def _should_merge_messages(self, msg1: Dict, msg2: Dict) -> bool:
        """判断两条消息是否应该合并"""
        # 同一发送者
        if msg1.get('sender') != msg2.get('sender'):
            return False
        
        # 时间间隔不超过5分钟
        try:
            time1 = datetime.fromisoformat(msg1.get('timestamp', ''))
            time2 = datetime.fromisoformat(msg2.get('timestamp', ''))
            if (time2 - time1).total_seconds() > 300:  # 5分钟
                return False
        except:
            return False
        
        # 内容相关性（简单判断）
        content1 = msg1.get('content', '').lower()
        content2 = msg2.get('content', '').lower()
        
        # 如果第二条消息很短，可能是补充说明
        if len(content2) < 50:
            return True
        
        # 检查关键词重叠
        words1 = set(content1.split())
        words2 = set(content2.split())
        overlap = len(words1.intersection(words2))
        
        return overlap > 2
    
    def _merge_message_group(self, messages: List[Dict]) -> Dict:
        """合并一组消息"""
        if len(messages) == 1:
            return messages[0]
        
        # 合并内容
        merged_content = ' '.join([msg.get('content', '') for msg in messages])
        
        # 使用第一条消息作为基础
        merged = messages[0].copy()
        merged['content'] = merged_content
        merged['merged_from'] = len(messages)
        merged['original_timestamps'] = [msg.get('timestamp') for msg in messages]
        
        return merged
