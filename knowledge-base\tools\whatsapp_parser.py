#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhatsApp消息解析器
支持多种WhatsApp导出格式的解析和标准化
"""

import re
import json
import csv
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging


class WhatsAppMessageParser:
    """WhatsApp消息解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger('WhatsAppMessageParser')
        
        # 时间格式模式
        self.time_patterns = [
            r'(\d{1,2}/\d{1,2}/\d{2,4}),?\s+(\d{1,2}:\d{2}(?::\d{2})?)\s*([AP]M)?',  # MM/DD/YYYY, HH:MM AM/PM
            r'(\d{1,2}\.\d{1,2}\.\d{2,4}),?\s+(\d{1,2}:\d{2}(?::\d{2})?)',  # DD.MM.YYYY, HH:MM
            r'(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}(?::\d{2})?)',  # YYYY-MM-DD HH:MM
            r'\[(\d{1,2}/\d{1,2}/\d{2,4}),?\s+(\d{1,2}:\d{2}(?::\d{2})?)\s*([AP]M)?\]',  # [MM/DD/YYYY, HH:MM AM/PM]
        ]
        
        # 消息模式
        self.message_patterns = [
            # 标准格式: 时间 - 发送者: 消息内容
            r'(.+?)\s*-\s*([^:]+?):\s*(.+)',
            # 带括号格式: 时间 发送者: 消息内容
            r'(.+?)\s+([^:]+?):\s*(.+)',
            # 简单格式: 发送者: 消息内容
            r'^([^:]+?):\s*(.+)',
        ]
        
        # 系统消息模式
        self.system_patterns = [
            r'.*加入了群聊.*',
            r'.*离开了群聊.*',
            r'.*更改了群聊主题.*',
            r'.*更改了群聊图标.*',
            r'.*删除了.*',
            r'.*撤回了一条消息.*',
            r'.*created group.*',
            r'.*left.*',
            r'.*joined.*',
            r'.*changed.*',
            r'.*deleted.*',
            r'.*removed.*',
            r'.*added.*',
            r'Messages and calls are end-to-end encrypted.*',
            r'消息和通话采用端到端加密.*',
        ]
    
    def parse_file(self, file_path: str) -> List[Dict]:
        """
        解析WhatsApp导出文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[Dict]: 解析后的消息列表
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        self.logger.info(f"开始解析文件: {file_path}")
        
        # 根据文件扩展名选择解析方法
        if file_path.suffix.lower() == '.json':
            messages = self._parse_json_file(file_path)
        elif file_path.suffix.lower() == '.csv':
            messages = self._parse_csv_file(file_path)
        else:
            # 默认按文本文件处理
            messages = self._parse_text_file(file_path)
        
        self.logger.info(f"解析完成，共 {len(messages)} 条消息")
        return messages
    
    def _parse_text_file(self, file_path: Path) -> List[Dict]:
        """解析文本格式的WhatsApp导出文件"""
        messages = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                content = f.read()
        
        # 按行分割，但考虑多行消息
        lines = content.split('\n')
        current_message = ""
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是新消息的开始
            if self._is_message_start(line):
                # 处理前一条消息
                if current_message:
                    parsed = self._parse_message_line(current_message)
                    if parsed:
                        messages.append(parsed)
                
                current_message = line
            else:
                # 续行，添加到当前消息
                if current_message:
                    current_message += " " + line
        
        # 处理最后一条消息
        if current_message:
            parsed = self._parse_message_line(current_message)
            if parsed:
                messages.append(parsed)
        
        return messages
    
    def _parse_json_file(self, file_path: Path) -> List[Dict]:
        """解析JSON格式的WhatsApp导出文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        messages = []
        
        # 处理不同的JSON结构
        if isinstance(data, list):
            # 直接是消息列表
            for item in data:
                parsed = self._parse_json_message(item)
                if parsed:
                    messages.append(parsed)
        elif isinstance(data, dict):
            # 可能包含元数据
            if 'messages' in data:
                for item in data['messages']:
                    parsed = self._parse_json_message(item)
                    if parsed:
                        messages.append(parsed)
            elif 'chat' in data:
                for item in data['chat']:
                    parsed = self._parse_json_message(item)
                    if parsed:
                        messages.append(parsed)
        
        return messages
    
    def _parse_csv_file(self, file_path: Path) -> List[Dict]:
        """解析CSV格式的WhatsApp导出文件"""
        messages = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            # 尝试自动检测分隔符
            sample = f.read(1024)
            f.seek(0)
            
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter
            
            reader = csv.DictReader(f, delimiter=delimiter)
            
            for row in reader:
                parsed = self._parse_csv_row(row)
                if parsed:
                    messages.append(parsed)
        
        return messages
    
    def _is_message_start(self, line: str) -> bool:
        """判断是否是新消息的开始"""
        # 检查是否包含时间戳模式
        for pattern in self.time_patterns:
            if re.search(pattern, line):
                return True
        return False
    
    def _parse_message_line(self, line: str) -> Optional[Dict]:
        """解析单行消息"""
        # 跳过系统消息
        if self._is_system_message(line):
            return None
        
        # 提取时间戳
        timestamp_info = self._extract_timestamp(line)
        if not timestamp_info:
            return None
        
        timestamp, remaining_text = timestamp_info
        
        # 解析发送者和内容
        sender_content = self._extract_sender_content(remaining_text)
        if not sender_content:
            return None
        
        sender, content = sender_content
        
        # 检测消息类型
        message_type = self._detect_message_type(content)
        
        return {
            'timestamp': timestamp,
            'sender': sender.strip(),
            'content': content.strip(),
            'message_type': message_type,
            'raw_line': line
        }
    
    def _parse_json_message(self, item: Dict) -> Optional[Dict]:
        """解析JSON格式的单条消息"""
        # 标准化字段名
        timestamp = item.get('timestamp') or item.get('time') or item.get('date')
        sender = item.get('sender') or item.get('from') or item.get('author')
        content = item.get('content') or item.get('message') or item.get('text')
        
        if not all([timestamp, sender, content]):
            return None
        
        # 标准化时间戳
        if isinstance(timestamp, (int, float)):
            timestamp = datetime.fromtimestamp(timestamp).isoformat()
        elif isinstance(timestamp, str):
            timestamp = self._normalize_timestamp(timestamp)
        
        message_type = self._detect_message_type(content)
        
        return {
            'timestamp': timestamp,
            'sender': str(sender).strip(),
            'content': str(content).strip(),
            'message_type': message_type,
            'raw_data': item
        }
    
    def _parse_csv_row(self, row: Dict) -> Optional[Dict]:
        """解析CSV格式的单行"""
        # 尝试不同的列名组合
        timestamp_cols = ['timestamp', 'time', 'date', 'datetime']
        sender_cols = ['sender', 'from', 'author', 'name']
        content_cols = ['content', 'message', 'text', 'body']
        
        timestamp = None
        sender = None
        content = None
        
        # 查找时间戳
        for col in timestamp_cols:
            if col in row and row[col]:
                timestamp = row[col]
                break
        
        # 查找发送者
        for col in sender_cols:
            if col in row and row[col]:
                sender = row[col]
                break
        
        # 查找内容
        for col in content_cols:
            if col in row and row[col]:
                content = row[col]
                break
        
        if not all([timestamp, sender, content]):
            return None
        
        # 标准化时间戳
        timestamp = self._normalize_timestamp(timestamp)
        message_type = self._detect_message_type(content)
        
        return {
            'timestamp': timestamp,
            'sender': str(sender).strip(),
            'content': str(content).strip(),
            'message_type': message_type,
            'raw_data': row
        }
    
    def _extract_timestamp(self, line: str) -> Optional[Tuple[str, str]]:
        """提取时间戳"""
        for pattern in self.time_patterns:
            match = re.search(pattern, line)
            if match:
                # 构建完整的时间戳
                groups = match.groups()
                
                if len(groups) >= 2:
                    date_part = groups[0]
                    time_part = groups[1]
                    am_pm = groups[2] if len(groups) > 2 else None
                    
                    # 标准化时间戳
                    timestamp = self._normalize_timestamp(f"{date_part} {time_part} {am_pm or ''}".strip())
                    
                    # 返回时间戳和剩余文本
                    remaining = line[match.end():].strip()
                    return timestamp, remaining
        
        return None
    
    def _extract_sender_content(self, text: str) -> Optional[Tuple[str, str]]:
        """提取发送者和内容"""
        for pattern in self.message_patterns:
            match = re.match(pattern, text)
            if match:
                groups = match.groups()
                if len(groups) >= 2:
                    sender = groups[-2]  # 倒数第二个是发送者
                    content = groups[-1]  # 最后一个是内容
                    return sender, content
        
        return None
    
    def _normalize_timestamp(self, timestamp_str: str) -> str:
        """标准化时间戳格式"""
        timestamp_str = timestamp_str.strip()
        
        # 常见的时间格式
        formats = [
            '%m/%d/%Y, %I:%M:%S %p',  # 12/31/2023, 11:59:59 PM
            '%m/%d/%Y, %I:%M %p',     # 12/31/2023, 11:59 PM
            '%m/%d/%y, %I:%M:%S %p',  # 12/31/23, 11:59:59 PM
            '%m/%d/%y, %I:%M %p',     # 12/31/23, 11:59 PM
            '%d.%m.%Y, %H:%M:%S',     # 31.12.2023, 23:59:59
            '%d.%m.%Y, %H:%M',        # 31.12.2023, 23:59
            '%Y-%m-%d %H:%M:%S',      # 2023-12-31 23:59:59
            '%Y-%m-%d %H:%M',         # 2023-12-31 23:59
            '%d/%m/%Y, %H:%M:%S',     # 31/12/2023, 23:59:59
            '%d/%m/%Y, %H:%M',        # 31/12/2023, 23:59
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(timestamp_str, fmt)
                return dt.isoformat()
            except ValueError:
                continue
        
        # 如果都不匹配，尝试更宽松的解析
        try:
            # 移除多余的空格和标点
            cleaned = re.sub(r'[,\[\]]', '', timestamp_str)
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()
            
            # 尝试解析
            for fmt in formats:
                try:
                    dt = datetime.strptime(cleaned, fmt)
                    return dt.isoformat()
                except ValueError:
                    continue
        except:
            pass
        
        # 如果仍然失败，返回当前时间
        self.logger.warning(f"无法解析时间戳: {timestamp_str}")
        return datetime.now().isoformat()
    
    def _detect_message_type(self, content: str) -> str:
        """检测消息类型"""
        content_lower = content.lower()
        
        # 媒体消息
        if any(keyword in content_lower for keyword in ['<media omitted>', '图片', '视频', '音频', '文档', '位置']):
            return 'media'
        
        # 链接消息
        if re.search(r'https?://', content):
            return 'link'
        
        # 系统消息
        if self._is_system_message(content):
            return 'system'
        
        # 默认为文本消息
        return 'text'
    
    def _is_system_message(self, content: str) -> bool:
        """判断是否是系统消息"""
        for pattern in self.system_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False
    
    def get_message_statistics(self, messages: List[Dict]) -> Dict[str, Any]:
        """获取消息统计信息"""
        if not messages:
            return {}
        
        # 基本统计
        total_messages = len(messages)
        senders = set(msg['sender'] for msg in messages)
        message_types = {}
        
        for msg in messages:
            msg_type = msg.get('message_type', 'unknown')
            message_types[msg_type] = message_types.get(msg_type, 0) + 1
        
        # 时间范围
        timestamps = [msg['timestamp'] for msg in messages if msg.get('timestamp')]
        if timestamps:
            start_time = min(timestamps)
            end_time = max(timestamps)
        else:
            start_time = end_time = None
        
        # 发送者统计
        sender_stats = {}
        for msg in messages:
            sender = msg['sender']
            sender_stats[sender] = sender_stats.get(sender, 0) + 1
        
        return {
            'total_messages': total_messages,
            'unique_senders': len(senders),
            'message_types': message_types,
            'time_range': {
                'start': start_time,
                'end': end_time
            },
            'sender_statistics': sender_stats,
            'top_senders': sorted(sender_stats.items(), key=lambda x: x[1], reverse=True)[:10]
        }
