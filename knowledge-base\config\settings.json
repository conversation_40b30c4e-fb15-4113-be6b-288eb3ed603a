{"system": {"name": "WhatsApp业务知识库系统", "version": "1.0.0", "description": "可持续维护的结构化知识库系统", "created_date": "2025-07-09", "last_updated": "2025-07-09"}, "database": {"format": "json", "encoding": "utf-8", "backup_enabled": true, "backup_frequency": "daily", "backup_retention_days": 30, "auto_save": true, "validation_on_save": true}, "search": {"default_results_limit": 20, "max_results_limit": 100, "enable_fuzzy_search": true, "fuzzy_threshold": 0.8, "search_fields": ["title", "description", "content.summary", "content.details", "tags"], "highlight_matches": true, "case_sensitive": false}, "versioning": {"enabled": true, "auto_increment": true, "version_format": "semantic", "keep_history": true, "max_history_versions": 10, "changelog_enabled": true}, "validation": {"strict_mode": true, "required_fields_check": true, "tag_validation": true, "relationship_validation": true, "duplicate_id_check": true, "schema_validation": true}, "categories": {"general": {"name": "通用知识库", "description": "存储技术问题、流程规范、最佳实践等通用性知识", "subcategories": {"technical": {"name": "技术问题", "description": "系统技术问题和解决方案"}, "process": {"name": "流程规范", "description": "业务流程和操作规范"}, "faq": {"name": "常见问题", "description": "频繁出现的问题和标准答案"}}}, "driver_conduct": {"name": "司机操守知识库", "description": "管理司机行为规范、违规案例、处罚标准等专业知识", "subcategories": {"regulations": {"name": "行为规范", "description": "司机应遵守的行为准则和服务标准"}, "violations": {"name": "违规案例", "description": "司机违规行为的记录和分析"}, "penalties": {"name": "处罚标准", "description": "不同违规行为对应的处罚措施"}}}}, "tags": {"max_tags_per_item": 10, "tag_format": "lowercase_underscore", "auto_suggest_tags": true, "tag_hierarchy_enabled": true, "custom_tags_allowed": true, "tag_validation_strict": false}, "relationships": {"max_related_items": 20, "auto_detect_relationships": true, "bidirectional_relationships": true, "relationship_types": ["related_items", "prerequisites", "follow_ups", "supersedes"]}, "export": {"supported_formats": ["json", "yaml", "csv", "markdown"], "default_format": "json", "include_metadata": true, "include_relationships": true, "compress_exports": false}, "import": {"supported_formats": ["json", "yaml", "csv"], "validate_on_import": true, "merge_strategy": "update_existing", "duplicate_handling": "skip", "batch_size": 100}, "notifications": {"enabled": true, "email_notifications": false, "log_level": "info", "notify_on_create": true, "notify_on_update": true, "notify_on_delete": true, "notify_on_validation_error": true}, "security": {"access_control_enabled": false, "audit_log_enabled": true, "data_encryption": false, "backup_encryption": false, "user_authentication": false}, "performance": {"cache_enabled": true, "cache_size_mb": 100, "cache_ttl_minutes": 60, "index_enabled": true, "lazy_loading": true, "pagination_enabled": true, "default_page_size": 20}, "ui": {"default_language": "zh-CN", "supported_languages": ["zh-CN", "en-US"], "theme": "default", "date_format": "YYYY-MM-DD", "time_format": "HH:mm:ss", "timezone": "Asia/Shanghai"}, "api": {"enabled": true, "version": "v1", "base_url": "/api/v1", "rate_limiting": {"enabled": false, "requests_per_minute": 100}, "cors_enabled": true, "authentication_required": false}, "logging": {"enabled": true, "log_level": "info", "log_file": "logs/knowledge_base.log", "max_log_size_mb": 10, "log_rotation": true, "log_retention_days": 30, "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "maintenance": {"auto_cleanup_enabled": true, "cleanup_frequency": "weekly", "remove_orphaned_relationships": true, "validate_data_integrity": true, "optimize_storage": true, "generate_reports": true}}