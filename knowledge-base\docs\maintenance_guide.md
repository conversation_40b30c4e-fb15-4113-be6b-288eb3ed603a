# 知识库系统维护手册

## 🔧 系统维护概述

本手册面向系统管理员，提供知识库系统的日常维护、故障排除和性能优化指导。

## 📋 日常维护任务

### 1. 数据完整性检查

**频率**：每日
**操作**：

```bash
# 验证所有知识条目
python knowledge_manager.py validate

# 检查数据一致性
python -c "
from knowledge_manager import KnowledgeManager
manager = KnowledgeManager()
items = manager.search_engine._load_all_items()
issues = manager.validator.check_data_consistency(items)
print('一致性检查结果:', issues)
"
```

**关注指标**：
- 无效条目数量
- 重复ID检测
- 断开的关联关系
- 孤立条目识别

### 2. 系统性能监控

**频率**：每周
**监控项目**：

```bash
# 检查缓存状态
python -c "
from search_engine import KnowledgeSearchEngine
from pathlib import Path
import json

with open('../config/settings.json') as f:
    settings = json.load(f)

engine = KnowledgeSearchEngine(Path('../data'), settings)
stats = engine.get_statistics()
print('知识库统计:', stats)
"
```

**性能指标**：
- 搜索响应时间
- 缓存命中率
- 内存使用情况
- 磁盘空间占用

### 3. 日志文件管理

**频率**：每周
**操作**：

```bash
# 检查日志文件大小
ls -lh logs/

# 清理过期日志（保留30天）
find logs/ -name "*.log" -mtime +30 -delete

# 压缩历史日志
gzip logs/knowledge_base.log.old
```

### 4. 备份管理

**频率**：每日
**备份脚本**：

```bash
#!/bin/bash
# backup_knowledge_base.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/knowledge_base"
SOURCE_DIR="../"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据文件
tar -czf $BACKUP_DIR/data_$DATE.tar.gz ../data/

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz ../config/

# 清理30天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "备份完成: $DATE"
```

## 🚨 故障排除

### 1. 搜索功能异常

**症状**：搜索无结果或结果不准确

**排查步骤**：

```bash
# 1. 检查数据文件完整性
python knowledge_manager.py validate

# 2. 清理缓存
python -c "
from knowledge_manager import KnowledgeManager
manager = KnowledgeManager()
manager.search_engine._cache = {}
manager.search_engine._cache_timestamp = None
print('缓存已清理')
"

# 3. 重建索引
python -c "
from knowledge_manager import KnowledgeManager
manager = KnowledgeManager()
items = manager.search_engine._load_all_items(force_reload=True)
print(f'重新加载了 {len(items)} 个条目')
"
```

### 2. Web界面无法访问

**症状**：Web界面启动失败或无法访问

**排查步骤**：

```bash
# 1. 检查端口占用
netstat -tulpn | grep :5000

# 2. 检查Python依赖
pip list | grep -E "(flask|json|yaml)"

# 3. 查看错误日志
python web_interface.py 2>&1 | tee web_error.log

# 4. 测试基础功能
python -c "
from knowledge_manager import KnowledgeManager
manager = KnowledgeManager()
print('管理器初始化成功')
"
```

### 3. 数据验证失败

**症状**：添加或更新条目时验证失败

**排查步骤**：

```bash
# 1. 检查数据格式
python -c "
import json
with open('problem_item.json') as f:
    data = json.load(f)
    print('JSON格式正确')
"

# 2. 详细验证信息
python -c "
from knowledge_manager import KnowledgeManager
import json

manager = KnowledgeManager()
with open('problem_item.json') as f:
    data = json.load(f)

result = manager.validator.validate_item(data)
if not result:
    print('验证错误:', manager.validator.get_validation_errors())
"
```

### 4. 性能问题

**症状**：系统响应缓慢

**优化措施**：

```bash
# 1. 启用缓存
# 在 config/settings.json 中设置：
{
  "performance": {
    "cache_enabled": true,
    "cache_size_mb": 100,
    "cache_ttl_minutes": 60
  }
}

# 2. 优化搜索配置
{
  "search": {
    "default_results_limit": 20,
    "max_results_limit": 100,
    "enable_fuzzy_search": false  // 关闭模糊搜索提升性能
  }
}

# 3. 清理无用数据
python -c "
from knowledge_manager import KnowledgeManager
manager = KnowledgeManager()
# 删除过期或无效的条目
"
```

## 🔄 系统更新

### 1. 配置文件更新

**更新标签配置**：

```bash
# 备份现有配置
cp config/tags.yaml config/tags.yaml.backup

# 编辑标签配置
vim config/tags.yaml

# 验证配置格式
python -c "
import yaml
with open('config/tags.yaml') as f:
    config = yaml.safe_load(f)
    print('标签配置格式正确')
"
```

**更新数据结构规范**：

```bash
# 备份现有规范
cp config/schema.json config/schema.json.backup

# 更新后验证所有数据
python knowledge_manager.py validate
```

### 2. 工具升级

**升级管理工具**：

```bash
# 备份现有工具
cp -r tools/ tools_backup/

# 更新工具文件后测试
python tools/knowledge_manager.py --help
python tools/web_interface.py --help
```

### 3. 数据迁移

**迁移到新版本**：

```bash
#!/bin/bash
# migrate_data.sh

# 1. 备份现有数据
tar -czf data_backup_$(date +%Y%m%d).tar.gz data/

# 2. 验证数据完整性
python knowledge_manager.py validate > validation_report.txt

# 3. 执行迁移脚本
python migrate_data.py

# 4. 验证迁移结果
python knowledge_manager.py validate
```

## 📊 监控和报告

### 1. 系统健康检查

**每日健康检查脚本**：

```python
#!/usr/bin/env python3
# health_check.py

from knowledge_manager import KnowledgeManager
from datetime import datetime
import json

def health_check():
    manager = KnowledgeManager()
    
    # 基本统计
    stats = manager.search_engine.get_statistics()
    
    # 验证结果
    validation = manager.validate_all_items()
    
    # 生成报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_items': stats['total_items'],
        'valid_items': len(validation['valid']),
        'invalid_items': len(validation['invalid']),
        'categories': stats['categories'],
        'health_status': 'healthy' if len(validation['invalid']) == 0 else 'warning'
    }
    
    # 保存报告
    with open(f'reports/health_report_{datetime.now().strftime("%Y%m%d")}.json', 'w') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"健康检查完成: {report['health_status']}")
    return report

if __name__ == '__main__':
    health_check()
```

### 2. 使用情况分析

**生成使用报告**：

```python
#!/usr/bin/env python3
# usage_report.py

import json
from collections import Counter
from datetime import datetime, timedelta

def generate_usage_report():
    # 分析搜索日志
    # 分析访问模式
    # 生成使用统计
    pass
```

## 🔐 安全维护

### 1. 访问控制

**检查文件权限**：

```bash
# 设置适当的文件权限
chmod 644 config/*.json config/*.yaml
chmod 644 data/**/*.json
chmod 755 tools/*.py

# 检查敏感文件
find . -name "*.json" -o -name "*.yaml" | xargs ls -la
```

### 2. 数据保护

**敏感信息检查**：

```bash
# 检查是否包含敏感信息
grep -r -i "password\|secret\|key\|token" data/
grep -r -i "phone\|email\|address" data/
```

## 📈 性能优化

### 1. 索引优化

**建立搜索索引**：

```python
# 可以考虑集成Elasticsearch或其他搜索引擎
# 目前使用内存缓存优化
```

### 2. 缓存策略

**优化缓存配置**：

```json
{
  "performance": {
    "cache_enabled": true,
    "cache_size_mb": 200,
    "cache_ttl_minutes": 120,
    "lazy_loading": true,
    "pagination_enabled": true
  }
}
```

## 📞 应急响应

### 1. 数据恢复

**从备份恢复**：

```bash
# 停止服务
pkill -f web_interface.py

# 恢复数据
tar -xzf /backup/knowledge_base/data_20250709_120000.tar.gz

# 验证数据
python knowledge_manager.py validate

# 重启服务
python web_interface.py &
```

### 2. 紧急维护

**系统紧急停机**：

```bash
# 停止所有相关进程
pkill -f knowledge_manager
pkill -f web_interface

# 创建维护页面
echo "系统维护中，请稍后访问" > maintenance.html

# 完成维护后重启
python web_interface.py &
```

---

*最后更新：2025-07-09*  
*版本：1.0.0*
