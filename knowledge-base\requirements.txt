# 知识库系统依赖包

# 核心依赖
flask>=2.3.0
pyyaml>=6.0
jsonschema>=4.17.0
pathlib2>=2.3.7

# Google Gemini AI
google-generativeai>=0.3.0

# 异步处理
asyncio-mqtt>=0.13.0

# 数据处理
pandas>=1.5.0
numpy>=1.24.0

# 文本处理
nltk>=3.8
textdistance>=4.5.0

# 时间处理
python-dateutil>=2.8.0

# 文件处理
chardet>=5.1.0

# 网络请求
requests>=2.28.0
aiohttp>=3.8.0

# 日志处理
colorlog>=6.7.0

# 开发和测试
pytest>=7.2.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# 可选依赖（用于扩展功能）
# elasticsearch>=8.6.0  # 如果需要Elasticsearch支持
# redis>=4.5.0          # 如果需要Redis缓存
# celery>=5.2.0         # 如果需要任务队列
# gunicorn>=20.1.0      # 生产环境部署
