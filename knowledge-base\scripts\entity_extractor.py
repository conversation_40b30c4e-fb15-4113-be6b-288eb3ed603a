#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实体提取器
负责从对话中识别和提取各种实体（司机ID、订单号、客户信息等）
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import hashlib


class EntityExtractor:
    """实体提取器"""
    
    def __init__(self, config: Dict):
        """
        初始化实体提取器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger('EntityExtractor')
        
        # 实体识别模式
        self.entity_patterns = self._load_entity_patterns()
        
        # 引用关系模式
        self.reference_patterns = self._load_reference_patterns()
        
        # 实体缓存
        self.entity_cache = {}
    
    def _load_entity_patterns(self) -> Dict[str, List[Dict]]:
        """加载实体识别模式"""
        return {
            'driver_id': [
                {
                    'pattern': r'@(\d{8,15})',
                    'confidence': 0.9,
                    'description': '@提及的司机ID'
                },
                {
                    'pattern': r'司机\s*(\d{8,15})',
                    'confidence': 0.8,
                    'description': '司机+数字ID'
                },
                {
                    'pattern': r'driver\s*(\d{8,15})',
                    'confidence': 0.8,
                    'description': 'driver+数字ID'
                }
            ],
            'order_id': [
                {
                    'pattern': r'订单\s*(\d{4,10})',
                    'confidence': 0.9,
                    'description': '订单+数字ID'
                },
                {
                    'pattern': r'order\s*(\d{4,10})',
                    'confidence': 0.9,
                    'description': 'order+数字ID'
                },
                {
                    'pattern': r'#(\d{4,10})',
                    'confidence': 0.7,
                    'description': '#号码格式'
                }
            ],
            'phone_number': [
                {
                    'pattern': r'\+?(\d{8,15})',
                    'confidence': 0.6,
                    'description': '电话号码格式'
                },
                {
                    'pattern': r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})',
                    'confidence': 0.7,
                    'description': '格式化电话号码'
                }
            ],
            'amount': [
                {
                    'pattern': r'\$(\d+(?:\.\d{2})?)',
                    'confidence': 0.9,
                    'description': '美元金额'
                },
                {
                    'pattern': r'(\d+(?:\.\d{2})?)\s*(?:元|dollar|usd)',
                    'confidence': 0.8,
                    'description': '带单位的金额'
                }
            ],
            'time_duration': [
                {
                    'pattern': r'(\d+)\s*(?:分钟|minutes?|mins?)',
                    'confidence': 0.8,
                    'description': '分钟时长'
                },
                {
                    'pattern': r'(\d+)\s*(?:小时|hours?|hrs?)',
                    'confidence': 0.8,
                    'description': '小时时长'
                },
                {
                    'pattern': r'(\d+)\s*(?:天|days?)',
                    'confidence': 0.8,
                    'description': '天数时长'
                }
            ],
            'location': [
                {
                    'pattern': r'([A-Z][a-z]+\s+[A-Z][a-z]+)',
                    'confidence': 0.6,
                    'description': '英文地名'
                },
                {
                    'pattern': r'([\u4e00-\u9fff]+(?:路|街|区|市|省|县))',
                    'confidence': 0.7,
                    'description': '中文地址'
                }
            ],
            'vehicle_info': [
                {
                    'pattern': r'([A-Z]{1,3}\d{1,4}[A-Z]?)',
                    'confidence': 0.7,
                    'description': '车牌号码'
                },
                {
                    'pattern': r'(婴儿椅|baby\s*seat|child\s*seat)',
                    'confidence': 0.9,
                    'description': '婴儿椅设备'
                }
            ]
        }
    
    def _load_reference_patterns(self) -> Dict[str, List[Dict]]:
        """加载引用关系模式"""
        return {
            'mention': [
                {
                    'pattern': r'@(\w+)',
                    'confidence': 0.9,
                    'description': '@提及'
                }
            ],
            'reply': [
                {
                    'pattern': r'回复\s*(.+)',
                    'confidence': 0.8,
                    'description': '回复关键词'
                },
                {
                    'pattern': r'针对\s*(.+)',
                    'confidence': 0.7,
                    'description': '针对关键词'
                }
            ],
            'reference': [
                {
                    'pattern': r'关于\s*(.+)',
                    'confidence': 0.7,
                    'description': '关于引用'
                },
                {
                    'pattern': r'就是\s*(.+)',
                    'confidence': 0.6,
                    'description': '就是引用'
                }
            ]
        }
    
    def extract_entities(self, conversation: Dict) -> List[Dict]:
        """
        从对话中提取实体
        
        Args:
            conversation: 对话数据
            
        Returns:
            List[Dict]: 提取的实体列表
        """
        content = conversation.get('content', '')
        entities = []
        
        # 提取各种类型的实体
        for entity_type, patterns in self.entity_patterns.items():
            extracted = self._extract_entity_type(content, entity_type, patterns)
            entities.extend(extracted)
        
        # 为每个实体添加上下文信息
        for entity in entities:
            entity['conversation_id'] = conversation.get('id')
            entity['timestamp'] = conversation.get('timestamp')
            entity['sender'] = conversation.get('sender')
            entity['group_id'] = conversation.get('group_id')
        
        return entities
    
    def _extract_entity_type(self, content: str, entity_type: str, patterns: List[Dict]) -> List[Dict]:
        """提取特定类型的实体"""
        entities = []
        
        for pattern_info in patterns:
            pattern = pattern_info['pattern']
            confidence = pattern_info['confidence']
            description = pattern_info['description']
            
            matches = re.finditer(pattern, content, re.IGNORECASE)
            
            for match in matches:
                entity_value = match.group(1) if match.groups() else match.group(0)
                
                # 创建实体对象
                entity = {
                    'id': self._generate_entity_id(entity_type, entity_value),
                    'type': entity_type,
                    'value': entity_value,
                    'confidence': confidence,
                    'description': description,
                    'position': {
                        'start': match.start(),
                        'end': match.end()
                    },
                    'context': self._extract_context(content, match.start(), match.end()),
                    'extracted_at': datetime.now().isoformat()
                }
                
                # 添加特定类型的处理
                entity = self._enhance_entity(entity, content)
                
                entities.append(entity)
        
        return entities
    
    def _generate_entity_id(self, entity_type: str, entity_value: str) -> str:
        """生成实体ID"""
        combined = f"{entity_type}_{entity_value}"
        return hashlib.md5(combined.encode()).hexdigest()[:12]
    
    def _extract_context(self, content: str, start: int, end: int, window: int = 20) -> str:
        """提取实体周围的上下文"""
        context_start = max(0, start - window)
        context_end = min(len(content), end + window)
        return content[context_start:context_end]
    
    def _enhance_entity(self, entity: Dict, content: str) -> Dict:
        """增强实体信息"""
        entity_type = entity['type']
        entity_value = entity['value']
        
        if entity_type == 'driver_id':
            entity['enhanced'] = self._enhance_driver_entity(entity_value, content)
        elif entity_type == 'order_id':
            entity['enhanced'] = self._enhance_order_entity(entity_value, content)
        elif entity_type == 'amount':
            entity['enhanced'] = self._enhance_amount_entity(entity_value, content)
        elif entity_type == 'time_duration':
            entity['enhanced'] = self._enhance_time_entity(entity_value, content)
        
        return entity
    
    def _enhance_driver_entity(self, driver_id: str, content: str) -> Dict:
        """增强司机实体信息"""
        enhanced = {
            'normalized_id': driver_id,
            'related_actions': [],
            'mentioned_issues': []
        }
        
        # 检查相关动作
        action_patterns = [
            (r'迟到', 'tardiness'),
            (r'违规', 'violation'),
            (r'投诉', 'complaint'),
            (r'表扬', 'praise'),
            (r'处罚', 'penalty'),
            (r'培训', 'training')
        ]
        
        for pattern, action in action_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                enhanced['related_actions'].append(action)
        
        return enhanced
    
    def _enhance_order_entity(self, order_id: str, content: str) -> Dict:
        """增强订单实体信息"""
        enhanced = {
            'normalized_id': order_id,
            'status_indicators': [],
            'related_issues': []
        }
        
        # 检查订单状态
        status_patterns = [
            (r'完成', 'completed'),
            (r'取消', 'cancelled'),
            (r'处理中', 'processing'),
            (r'分配', 'assigned'),
            (r'确认', 'confirmed')
        ]
        
        for pattern, status in status_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                enhanced['status_indicators'].append(status)
        
        return enhanced
    
    def _enhance_amount_entity(self, amount: str, content: str) -> Dict:
        """增强金额实体信息"""
        enhanced = {
            'normalized_amount': float(amount.replace(',', '')),
            'currency': 'USD',  # 默认美元
            'context_type': 'unknown'
        }
        
        # 检查金额上下文
        context_patterns = [
            (r'罚款', 'penalty'),
            (r'补偿', 'compensation'),
            (r'价格', 'price'),
            (r'费用', 'fee'),
            (r'退款', 'refund')
        ]
        
        for pattern, context_type in context_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                enhanced['context_type'] = context_type
                break
        
        return enhanced
    
    def _enhance_time_entity(self, duration: str, content: str) -> Dict:
        """增强时间实体信息"""
        enhanced = {
            'normalized_duration': int(duration),
            'unit': 'unknown',
            'context_type': 'unknown'
        }
        
        # 确定时间单位
        if re.search(r'分钟|minutes?|mins?', content, re.IGNORECASE):
            enhanced['unit'] = 'minutes'
        elif re.search(r'小时|hours?|hrs?', content, re.IGNORECASE):
            enhanced['unit'] = 'hours'
        elif re.search(r'天|days?', content, re.IGNORECASE):
            enhanced['unit'] = 'days'
        
        # 检查时间上下文
        context_patterns = [
            (r'迟到', 'delay'),
            (r'等待', 'waiting'),
            (r'处理', 'processing'),
            (r'暂停', 'suspension')
        ]
        
        for pattern, context_type in context_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                enhanced['context_type'] = context_type
                break
        
        return enhanced
    
    def extract_references(self, conversations: List[Dict]) -> List[Dict]:
        """
        提取对话间的引用关系
        
        Args:
            conversations: 对话列表
            
        Returns:
            List[Dict]: 引用关系列表
        """
        references = []
        
        # 按时间排序
        sorted_conversations = sorted(
            conversations, 
            key=lambda x: x.get('timestamp', '')
        )
        
        for i, conversation in enumerate(sorted_conversations):
            content = conversation.get('content', '')
            
            # 检查@提及
            mentions = self._extract_mentions(conversation, sorted_conversations[:i])
            references.extend(mentions)
            
            # 检查回复关系
            replies = self._extract_replies(conversation, sorted_conversations[:i])
            references.extend(replies)
            
            # 检查实体引用
            entity_refs = self._extract_entity_references(conversation, sorted_conversations[:i])
            references.extend(entity_refs)
        
        return references
    
    def _extract_mentions(self, conversation: Dict, previous_conversations: List[Dict]) -> List[Dict]:
        """提取@提及关系"""
        mentions = []
        content = conversation.get('content', '')
        
        # 查找@提及
        mention_matches = re.finditer(r'@(\w+)', content)
        
        for match in mention_matches:
            mentioned_entity = match.group(1)
            
            # 查找被提及的实体在之前对话中的出现
            for prev_conv in reversed(previous_conversations):  # 从最近的开始查找
                if mentioned_entity in prev_conv.get('content', ''):
                    mentions.append({
                        'type': 'mention',
                        'source_conversation': conversation.get('id'),
                        'target_conversation': prev_conv.get('id'),
                        'mentioned_entity': mentioned_entity,
                        'confidence': 0.8,
                        'timestamp': conversation.get('timestamp')
                    })
                    break
        
        return mentions
    
    def _extract_replies(self, conversation: Dict, previous_conversations: List[Dict]) -> List[Dict]:
        """提取回复关系"""
        replies = []
        content = conversation.get('content', '').lower()
        
        # 回复关键词
        reply_keywords = ['回复', '针对', '关于', '就是', '这个', '那个']
        
        has_reply_keyword = any(keyword in content for keyword in reply_keywords)
        
        if has_reply_keyword and previous_conversations:
            # 简单策略：回复最近的一条消息
            latest_conv = previous_conversations[-1]
            
            replies.append({
                'type': 'reply',
                'source_conversation': conversation.get('id'),
                'target_conversation': latest_conv.get('id'),
                'confidence': 0.6,
                'timestamp': conversation.get('timestamp')
            })
        
        return replies
    
    def _extract_entity_references(self, conversation: Dict, previous_conversations: List[Dict]) -> List[Dict]:
        """提取实体引用关系"""
        references = []
        
        # 提取当前对话中的实体
        current_entities = self.extract_entities(conversation)
        
        for entity in current_entities:
            entity_value = entity['value']
            entity_type = entity['type']
            
            # 在之前的对话中查找相同实体
            for prev_conv in reversed(previous_conversations):
                prev_entities = self.extract_entities(prev_conv)
                
                for prev_entity in prev_entities:
                    if (prev_entity['type'] == entity_type and 
                        prev_entity['value'] == entity_value):
                        
                        references.append({
                            'type': 'entity_reference',
                            'source_conversation': conversation.get('id'),
                            'target_conversation': prev_conv.get('id'),
                            'entity_type': entity_type,
                            'entity_value': entity_value,
                            'confidence': 0.9,
                            'timestamp': conversation.get('timestamp')
                        })
                        break
        
        return references
    
    def build_entity_graph(self, entities: List[Dict], references: List[Dict]) -> Dict[str, Any]:
        """
        构建实体关系图
        
        Args:
            entities: 实体列表
            references: 引用关系列表
            
        Returns:
            Dict: 实体关系图数据
        """
        # 统计实体
        entity_stats = {}
        for entity in entities:
            entity_type = entity['type']
            entity_value = entity['value']
            
            key = f"{entity_type}:{entity_value}"
            if key not in entity_stats:
                entity_stats[key] = {
                    'type': entity_type,
                    'value': entity_value,
                    'count': 0,
                    'conversations': set(),
                    'first_seen': entity['timestamp'],
                    'last_seen': entity['timestamp']
                }
            
            entity_stats[key]['count'] += 1
            entity_stats[key]['conversations'].add(entity['conversation_id'])
            
            # 更新时间范围
            if entity['timestamp'] < entity_stats[key]['first_seen']:
                entity_stats[key]['first_seen'] = entity['timestamp']
            if entity['timestamp'] > entity_stats[key]['last_seen']:
                entity_stats[key]['last_seen'] = entity['timestamp']
        
        # 转换为图数据格式
        nodes = []
        for key, stats in entity_stats.items():
            nodes.append({
                'id': key,
                'type': stats['type'],
                'value': stats['value'],
                'count': stats['count'],
                'conversations': list(stats['conversations']),
                'first_seen': stats['first_seen'],
                'last_seen': stats['last_seen']
            })
        
        # 构建边（基于共现关系）
        edges = []
        conversations_entities = {}
        
        # 按对话分组实体
        for entity in entities:
            conv_id = entity['conversation_id']
            if conv_id not in conversations_entities:
                conversations_entities[conv_id] = []
            conversations_entities[conv_id].append(f"{entity['type']}:{entity['value']}")
        
        # 计算共现关系
        for conv_id, conv_entities in conversations_entities.items():
            for i, entity1 in enumerate(conv_entities):
                for entity2 in conv_entities[i+1:]:
                    edge_id = f"{entity1}--{entity2}"
                    
                    # 查找是否已存在这条边
                    existing_edge = next((e for e in edges if e['id'] == edge_id), None)
                    
                    if existing_edge:
                        existing_edge['weight'] += 1
                        existing_edge['conversations'].append(conv_id)
                    else:
                        edges.append({
                            'id': edge_id,
                            'source': entity1,
                            'target': entity2,
                            'weight': 1,
                            'conversations': [conv_id],
                            'type': 'co_occurrence'
                        })
        
        return {
            'nodes': nodes,
            'edges': edges,
            'statistics': {
                'total_entities': len(entities),
                'unique_entities': len(entity_stats),
                'entity_types': len(set(e['type'] for e in entities)),
                'co_occurrence_edges': len(edges)
            }
        }
