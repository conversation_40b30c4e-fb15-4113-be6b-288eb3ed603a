# 知识库系统 (Knowledge Base System)

## 系统概述

这是一个可持续维护的结构化知识库系统，专为WhatsApp业务群组管理而设计。系统包含两个核心模块：

1. **通用知识库模块** - 存储技术问题、流程规范、最佳实践等通用性知识
2. **司机操守知识库模块** - 管理司机行为规范、违规案例、处罚标准等专业知识

## 系统架构

```
knowledge-base/
├── config/                    # 配置文件
│   ├── schema.json           # 数据结构定义
│   ├── tags.yaml            # 标签体系定义
│   └── settings.json        # 系统设置
├── data/                     # 知识库数据
│   ├── general/             # 通用知识库
│   │   ├── technical/       # 技术问题
│   │   ├── process/         # 流程规范
│   │   └── faq/            # 常见问题
│   └── driver-conduct/      # 司机操守知识库
│       ├── regulations/     # 行为规范
│       ├── violations/      # 违规案例
│       └── penalties/       # 处罚标准
├── tools/                   # 管理工具
│   ├── knowledge_manager.py # 主管理工具
│   ├── search_engine.py    # 搜索引擎
│   └── validator.py        # 数据验证
├── templates/              # 模板文件
└── docs/                   # 文档
```

## 核心特性

### 🏗️ 结构化数据格式
- 采用JSON格式存储，便于程序化处理
- 统一的数据结构规范，确保一致性
- 支持复杂的关联关系和元数据

### 🏷️ 多维度标签系统
- 支持层级标签分类
- 多标签组合查询
- 动态标签管理

### 🔍 强大的搜索功能
- 全文搜索支持
- 标签过滤和分类浏览
- 智能关联推荐

### 📝 版本控制机制
- 语义化版本管理
- 完整的变更历史
- 支持回滚操作

### 🔧 便捷的管理工具
- 命令行管理界面
- 数据验证和完整性检查
- 批量操作支持

## 快速开始

### 1. 初始化知识库
```bash
python tools/knowledge_manager.py init
```

### 2. 添加知识条目
```bash
python tools/knowledge_manager.py add --type general --file new_item.json
```

### 3. 搜索知识
```bash
python tools/knowledge_manager.py search "订单处理" --tags order_processing
```

### 4. 更新条目
```bash
python tools/knowledge_manager.py update item_id --file updated_item.json
```

## 数据结构

每个知识条目遵循统一的JSON结构：

```json
{
  "id": "unique_identifier",
  "title": "知识条目标题",
  "description": "详细描述",
  "category": "主分类",
  "subcategory": "子分类",
  "tags": ["标签1", "标签2"],
  "content": {
    "summary": "简要总结",
    "details": "详细内容",
    "examples": ["示例1", "示例2"]
  },
  "metadata": {
    "created_at": "2025-07-09T10:00:00Z",
    "updated_at": "2025-07-09T10:00:00Z",
    "version": "1.0.0",
    "author": "创建者",
    "source": "数据来源"
  }
}
```

## 标签体系

### 通用知识库标签
- **业务类型**: order_processing, customer_service, pricing, third_party
- **紧急程度**: urgent, important, normal
- **处理状态**: pending, in_progress, completed
- **涉及部门**: customer_service, driver, backend, management

### 司机操守知识库标签
- **违规类型**: tardiness, no_show, service_attitude, capability_mismatch
- **严重程度**: minor, moderate, serious, critical
- **处罚类型**: warning, financial_penalty, service_suspension, permanent_ban
- **证据类型**: screenshot, audio, customer_complaint, system_record

## 维护指南

详细的维护和使用指南请参考 `docs/` 目录下的相关文档：

- [使用指南](docs/user_guide.md)
- [维护手册](docs/maintenance_guide.md)
- [API文档](docs/api_reference.md)
- [开发指南](docs/development_guide.md)

## 版本信息

- **当前版本**: 1.0.0
- **创建日期**: 2025-07-09
- **最后更新**: 2025-07-09

## 许可证

本项目采用 MIT 许可证。详情请参见 LICENSE 文件。
