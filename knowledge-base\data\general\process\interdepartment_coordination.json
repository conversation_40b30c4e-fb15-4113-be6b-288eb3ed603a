{"id": "interdepartment_coordination_001", "title": "跨部门信息协调流程", "description": "各部门之间重要信息传递和协调处理的标准流程", "category": "process", "subcategory": "communication", "tags": ["communication", "interdepartment", "coordination", "information_sharing", "important"], "content": {"summary": "确保各部门之间重要信息能够及时、准确传递的协调机制", "details": "跨部门协调是保证业务顺畅运行的关键环节。通过专门的协调群组，各部门可以及时分享重要信息、协调资源配置、处理跨部门问题。这种机制确保了信息的透明度和决策的一致性。", "examples": ["后台系统提醒需要注意特定订单", "业务操作提醒的跨部门传递", "重要政策变更的部门通知"], "related_cases": ["系统升级跨部门协调案例", "紧急事件跨部门响应案例", "政策变更部门同步案例"], "steps": [{"step": 1, "description": "识别需要跨部门协调的事项", "notes": "判断事项是否涉及多个部门或需要统一行动"}, {"step": 2, "description": "选择合适的沟通渠道", "notes": "根据紧急程度选择群组通知或直接联系"}, {"step": 3, "description": "发送协调信息", "notes": "清晰描述事项内容、影响范围和所需行动"}, {"step": 4, "description": "确认各部门收到信息", "notes": "确保相关部门都已收到并理解信息"}, {"step": 5, "description": "跟踪执行情况", "notes": "监控各部门的响应和执行情况"}, {"step": 6, "description": "总结协调结果", "notes": "记录协调过程和结果，为后续改进提供参考"}]}, "metadata": {"created_at": "2025-07-09T10:45:00Z", "updated_at": "2025-07-09T10:45:00Z", "version": "1.0.0", "author": "系统管理员", "last_editor": "系统管理员", "source": "WhatsApp群组8各部门信息交流群聊天记录", "confidence_level": "high", "review_status": "approved", "expiry_date": null}, "relationships": {"related_items": ["emergency_response_protocol_001", "information_management_001"], "prerequisites": ["department_structure_understanding_001"], "follow_ups": ["coordination_effectiveness_evaluation_001"], "supersedes": []}}