{"id": "service_capability_mismatch_001", "title": "司机服务能力不匹配违规处理", "description": "司机接受超出自身服务能力订单的违规行为处理规范", "category": "violations", "subcategory": "capability_mismatch", "tags": ["capability_mismatch", "service_attitude", "customer_complaint", "moderate", "training_required"], "content": {"summary": "司机应该根据自身服务能力接单，不应接受无法提供的特殊服务要求", "details": "司机在接单前必须确认自己能够提供订单要求的所有服务，包括特殊设备（如婴儿椅）、特殊路线、特殊时间要求等。如果司机接单后发现无法提供相应服务，不仅影响客户体验，还浪费客服人员的协调时间。", "examples": ["订单106838顾客添加婴儿椅，司机无法提供导致退单", "司机接受长途订单但车辆状况不适合", "司机接受夜间订单但不熟悉路线"], "related_cases": ["婴儿椅服务能力不匹配案例", "特殊路线服务能力问题", "时间安排能力不匹配案例"]}, "metadata": {"created_at": "2025-07-09T11:45:00Z", "updated_at": "2025-07-09T11:45:00Z", "version": "1.0.0", "author": "系统管理员", "last_editor": "系统管理员", "source": "WhatsApp群组7司机问题处理群聊天记录", "confidence_level": "high", "review_status": "approved", "expiry_date": null}, "relationships": {"related_items": ["driver_service_standards_001", "special_service_requirements_001"], "prerequisites": ["driver_capability_assessment_001"], "follow_ups": ["driver_training_program_001"], "supersedes": []}, "driver_conduct_specific": {"violation_type": "capability_mismatch", "severity_level": "moderate", "penalty_type": "additional_training", "penalty_details": {"financial_amount": null, "suspension_days": null, "additional_requirements": ["参加服务能力评估", "完成相关技能培训", "更新个人服务能力档案"]}, "evidence_types": ["customer_complaint", "chat_log", "system_record"], "case_studies": [{"case_id": "capability_mismatch_001", "driver_info": "未指定司机", "incident_description": "订单106838顾客添加婴儿椅服务，司机接单后发现无法提供，导致客服重新协调", "resolution": "要求司机如无法提供婴儿椅服务不应接单，避免浪费协调时间", "outcome": "建立服务能力标签系统，提前确认司机服务能力"}]}}