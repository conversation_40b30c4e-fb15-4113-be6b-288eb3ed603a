#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化数据生成器
负责生成静态HTML仪表盘所需的JSON数据文件
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
from pathlib import Path
from collections import Counter, defaultdict
import calendar


class VisualizationGenerator:
    """可视化数据生成器"""
    
    def __init__(self, config: Dict):
        """
        初始化可视化数据生成器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger('VisualizationGenerator')
        self.base_path = Path(__file__).parent.parent
        self.static_path = self.base_path / "static"
        
        # 确保静态文件目录存在
        self.static_path.mkdir(exist_ok=True)
        (self.static_path / "data").mkdir(exist_ok=True)
        (self.static_path / "css").mkdir(exist_ok=True)
        (self.static_path / "js").mkdir(exist_ok=True)
    
    def generate_visualization_data(self, conversations: List[Dict], entities: List[Dict], 
                                  knowledge_items: List[Dict]) -> Dict[str, Any]:
        """
        生成所有可视化数据
        
        Args:
            conversations: 对话列表
            entities: 实体列表
            knowledge_items: 知识条目列表
            
        Returns:
            Dict: 包含所有可视化数据的字典
        """
        self.logger.info("开始生成可视化数据")
        
        visualization_data = {
            'dashboard_stats': self._generate_dashboard_stats(conversations, entities, knowledge_items),
            'knowledge_distribution': self._generate_knowledge_distribution(knowledge_items),
            'time_trends': self._generate_time_trends(conversations, knowledge_items),
            'tag_analysis': self._generate_tag_analysis(conversations, knowledge_items),
            'entity_statistics': self._generate_entity_statistics(entities),
            'group_analysis': self._generate_group_analysis(conversations),
            'driver_conduct_stats': self._generate_driver_conduct_stats(knowledge_items),
            'processing_timeline': self._generate_processing_timeline(conversations)
        }
        
        self.logger.info("可视化数据生成完成")
        return visualization_data
    
    def _generate_dashboard_stats(self, conversations: List[Dict], entities: List[Dict], 
                                knowledge_items: List[Dict]) -> Dict[str, Any]:
        """生成仪表盘统计数据"""
        # 基本统计
        total_conversations = len(conversations)
        total_entities = len(entities)
        total_knowledge = len(knowledge_items)
        
        # 按群组统计对话
        group_stats = Counter(conv.get('group_id', 'unknown') for conv in conversations)
        
        # 按类型统计知识条目
        knowledge_by_category = Counter(item.get('category', 'unknown') for item in knowledge_items)
        
        # 按类型统计实体
        entity_by_type = Counter(entity.get('type', 'unknown') for entity in entities)
        
        # 今日统计
        today = datetime.now().date()
        today_conversations = [
            conv for conv in conversations 
            if self._parse_date(conv.get('timestamp', '')).date() == today
        ]
        
        return {
            'overview': {
                'total_conversations': total_conversations,
                'total_entities': total_entities,
                'total_knowledge_items': total_knowledge,
                'today_conversations': len(today_conversations)
            },
            'group_distribution': dict(group_stats),
            'knowledge_categories': dict(knowledge_by_category),
            'entity_types': dict(entity_by_type),
            'generated_at': datetime.now().isoformat()
        }
    
    def _generate_knowledge_distribution(self, knowledge_items: List[Dict]) -> Dict[str, Any]:
        """生成知识库分布数据"""
        # 通用知识库统计
        general_items = [item for item in knowledge_items 
                        if item.get('category') in ['technical', 'process', 'faq']]
        
        general_stats = {
            'technical': len([item for item in general_items if item.get('category') == 'technical']),
            'process': len([item for item in general_items if item.get('category') == 'process']),
            'faq': len([item for item in general_items if item.get('category') == 'faq'])
        }
        
        # 司机操守知识库统计
        driver_items = [item for item in knowledge_items 
                       if item.get('category') in ['violations', 'regulations', 'penalties']]
        
        driver_stats = {
            'violations': len([item for item in driver_items if item.get('category') == 'violations']),
            'regulations': len([item for item in driver_items if item.get('category') == 'regulations']),
            'penalties': len([item for item in driver_items if item.get('category') == 'penalties'])
        }
        
        # 标签分布
        all_tags = []
        for item in knowledge_items:
            all_tags.extend(item.get('tags', []))
        
        tag_distribution = dict(Counter(all_tags).most_common(20))
        
        return {
            'general_knowledge': general_stats,
            'driver_conduct': driver_stats,
            'tag_distribution': tag_distribution,
            'total_by_type': {
                'general': len(general_items),
                'driver_conduct': len(driver_items)
            }
        }
    
    def _generate_time_trends(self, conversations: List[Dict], knowledge_items: List[Dict]) -> Dict[str, Any]:
        """生成时间趋势数据"""
        # 按日期统计对话数量
        daily_conversations = defaultdict(int)
        for conv in conversations:
            date = self._parse_date(conv.get('timestamp', '')).date()
            daily_conversations[date.isoformat()] += 1
        
        # 按日期统计知识条目创建
        daily_knowledge = defaultdict(int)
        for item in knowledge_items:
            created_at = item.get('metadata', {}).get('created_at', '')
            if created_at:
                date = self._parse_date(created_at).date()
                daily_knowledge[date.isoformat()] += 1
        
        # 按小时统计对话活跃度
        hourly_activity = defaultdict(int)
        for conv in conversations:
            hour = self._parse_date(conv.get('timestamp', '')).hour
            hourly_activity[hour] += 1
        
        # 按星期统计
        weekly_activity = defaultdict(int)
        for conv in conversations:
            weekday = self._parse_date(conv.get('timestamp', '')).weekday()
            day_name = calendar.day_name[weekday]
            weekly_activity[day_name] += 1
        
        return {
            'daily_conversations': dict(daily_conversations),
            'daily_knowledge_creation': dict(daily_knowledge),
            'hourly_activity': dict(hourly_activity),
            'weekly_activity': dict(weekly_activity)
        }
    
    def _generate_tag_analysis(self, conversations: List[Dict], knowledge_items: List[Dict]) -> Dict[str, Any]:
        """生成标签分析数据"""
        # 从对话中收集生成的标签
        conversation_tags = defaultdict(int)
        tag_types = defaultdict(lambda: defaultdict(int))
        
        for conv in conversations:
            generated_tags = conv.get('generated_tags', [])
            for tag in generated_tags:
                tag_value = tag.get('value', '')
                tag_type = tag.get('type', 'unknown')
                
                conversation_tags[tag_value] += 1
                tag_types[tag_type][tag_value] += 1
        
        # 从知识条目中收集标签
        knowledge_tags = defaultdict(int)
        for item in knowledge_items:
            for tag in item.get('tags', []):
                knowledge_tags[tag] += 1
        
        # 标签共现分析
        tag_cooccurrence = self._calculate_tag_cooccurrence(conversations)
        
        return {
            'conversation_tags': dict(conversation_tags),
            'tag_types': {k: dict(v) for k, v in tag_types.items()},
            'knowledge_tags': dict(knowledge_tags),
            'tag_cooccurrence': tag_cooccurrence,
            'top_tags': dict(Counter(conversation_tags).most_common(15))
        }
    
    def _generate_entity_statistics(self, entities: List[Dict]) -> Dict[str, Any]:
        """生成实体统计数据"""
        # 按类型统计实体
        entity_counts = Counter(entity.get('type', 'unknown') for entity in entities)
        
        # 按置信度统计
        confidence_ranges = {
            'high': len([e for e in entities if e.get('confidence', 0) >= 0.8]),
            'medium': len([e for e in entities if 0.6 <= e.get('confidence', 0) < 0.8]),
            'low': len([e for e in entities if e.get('confidence', 0) < 0.6])
        }
        
        # 最频繁的实体
        entity_values = Counter(f"{entity.get('type', '')}:{entity.get('value', '')}" 
                               for entity in entities)
        
        # 按群组统计实体
        group_entities = defaultdict(lambda: defaultdict(int))
        for entity in entities:
            group_id = entity.get('group_id', 'unknown')
            entity_type = entity.get('type', 'unknown')
            group_entities[group_id][entity_type] += 1
        
        return {
            'entity_counts': dict(entity_counts),
            'confidence_distribution': confidence_ranges,
            'top_entities': dict(entity_values.most_common(20)),
            'group_entities': {k: dict(v) for k, v in group_entities.items()}
        }
    
    def _generate_group_analysis(self, conversations: List[Dict]) -> Dict[str, Any]:
        """生成群组分析数据"""
        group_data = defaultdict(lambda: {
            'total_messages': 0,
            'unique_senders': set(),
            'message_lengths': [],
            'time_distribution': defaultdict(int)
        })
        
        for conv in conversations:
            group_id = conv.get('group_id', 'unknown')
            sender = conv.get('sender', 'unknown')
            content = conv.get('content', '')
            timestamp = conv.get('timestamp', '')
            
            group_data[group_id]['total_messages'] += 1
            group_data[group_id]['unique_senders'].add(sender)
            group_data[group_id]['message_lengths'].append(len(content))
            
            # 时间分布
            hour = self._parse_date(timestamp).hour
            group_data[group_id]['time_distribution'][hour] += 1
        
        # 转换为可序列化格式
        result = {}
        for group_id, data in group_data.items():
            result[group_id] = {
                'total_messages': data['total_messages'],
                'unique_senders': len(data['unique_senders']),
                'avg_message_length': sum(data['message_lengths']) / len(data['message_lengths']) if data['message_lengths'] else 0,
                'time_distribution': dict(data['time_distribution'])
            }
        
        return result
    
    def _generate_driver_conduct_stats(self, knowledge_items: List[Dict]) -> Dict[str, Any]:
        """生成司机操守统计数据"""
        driver_items = [item for item in knowledge_items 
                       if 'driver_conduct_specific' in item]
        
        if not driver_items:
            return {
                'violation_types': {},
                'severity_levels': {},
                'penalty_types': {},
                'evidence_types': {},
                'total_cases': 0
            }
        
        # 违规类型统计
        violation_types = Counter()
        severity_levels = Counter()
        penalty_types = Counter()
        evidence_types = Counter()
        
        for item in driver_items:
            conduct_data = item.get('driver_conduct_specific', {})
            
            violation_type = conduct_data.get('violation_type')
            if violation_type:
                violation_types[violation_type] += 1
            
            severity = conduct_data.get('severity_level')
            if severity:
                severity_levels[severity] += 1
            
            penalty = conduct_data.get('penalty_type')
            if penalty:
                penalty_types[penalty] += 1
            
            evidence_list = conduct_data.get('evidence_types', [])
            for evidence in evidence_list:
                evidence_types[evidence] += 1
        
        return {
            'violation_types': dict(violation_types),
            'severity_levels': dict(severity_levels),
            'penalty_types': dict(penalty_types),
            'evidence_types': dict(evidence_types),
            'total_cases': len(driver_items)
        }
    
    def _generate_processing_timeline(self, conversations: List[Dict]) -> Dict[str, Any]:
        """生成处理时间线数据"""
        # 按时间排序对话
        sorted_conversations = sorted(
            conversations, 
            key=lambda x: self._parse_date(x.get('timestamp', ''))
        )
        
        timeline_events = []
        for conv in sorted_conversations:
            timestamp = conv.get('timestamp', '')
            sender = conv.get('sender', '')
            content = conv.get('content', '')
            group_id = conv.get('group_id', '')
            
            # 识别重要事件
            event_type = self._classify_event_type(content)
            
            timeline_events.append({
                'timestamp': timestamp,
                'sender': sender,
                'content': content[:100] + '...' if len(content) > 100 else content,
                'group_id': group_id,
                'event_type': event_type
            })
        
        return {
            'events': timeline_events[-50:],  # 只保留最近50个事件
            'total_events': len(timeline_events)
        }
    
    def _classify_event_type(self, content: str) -> str:
        """分类事件类型"""
        content_lower = content.lower()
        
        if any(word in content_lower for word in ['订单', 'order']):
            return 'order'
        elif any(word in content_lower for word in ['司机', 'driver']):
            return 'driver'
        elif any(word in content_lower for word in ['投诉', 'complaint']):
            return 'complaint'
        elif any(word in content_lower for word in ['系统', 'system']):
            return 'system'
        else:
            return 'general'
    
    def _calculate_tag_cooccurrence(self, conversations: List[Dict]) -> Dict[str, int]:
        """计算标签共现频率"""
        cooccurrence = defaultdict(int)
        
        for conv in conversations:
            tags = [tag.get('value', '') for tag in conv.get('generated_tags', [])]
            
            # 计算标签对的共现
            for i, tag1 in enumerate(tags):
                for tag2 in tags[i+1:]:
                    pair = tuple(sorted([tag1, tag2]))
                    cooccurrence[f"{pair[0]}--{pair[1]}"] += 1
        
        # 只返回频率较高的共现对
        return {k: v for k, v in cooccurrence.items() if v >= 2}
    
    def _parse_date(self, timestamp_str: str) -> datetime:
        """解析时间戳字符串"""
        try:
            return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        except:
            return datetime.now()
    
    def generate_html_files(self) -> Dict[str, str]:
        """
        生成静态HTML文件
        
        Returns:
            Dict: 生成的HTML文件路径
        """
        html_files = {}
        
        # 生成主仪表盘
        dashboard_html = self._generate_dashboard_html()
        dashboard_path = self.static_path / "dashboard.html"
        with open(dashboard_path, 'w', encoding='utf-8') as f:
            f.write(dashboard_html)
        html_files['dashboard'] = str(dashboard_path)
        
        # 生成知识网络页面
        network_html = self._generate_network_html()
        network_path = self.static_path / "knowledge_network.html"
        with open(network_path, 'w', encoding='utf-8') as f:
            f.write(network_html)
        html_files['network'] = str(network_path)
        
        # 生成趋势分析页面
        trends_html = self._generate_trends_html()
        trends_path = self.static_path / "trend_analysis.html"
        with open(trends_path, 'w', encoding='utf-8') as f:
            f.write(trends_html)
        html_files['trends'] = str(trends_path)
        
        # 生成CSS和JS文件
        self._generate_static_assets()
        
        return html_files
    
    def _generate_dashboard_html(self) -> str:
        """生成仪表盘HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库可视化仪表盘</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>📊 知识库可视化仪表盘</h1>
            <p>WhatsApp业务知识库分析报告</p>
        </header>
        
        <nav class="navigation">
            <a href="dashboard.html" class="nav-link active">📊 总览</a>
            <a href="knowledge_network.html" class="nav-link">🕸️ 知识网络</a>
            <a href="trend_analysis.html" class="nav-link">📈 趋势分析</a>
        </nav>
        
        <main class="main-content">
            <!-- 统计卡片 -->
            <section class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">💬</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalConversations">-</div>
                        <div class="stat-label">总对话数</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📚</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalKnowledge">-</div>
                        <div class="stat-label">知识条目</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🏷️</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalEntities">-</div>
                        <div class="stat-label">识别实体</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <div class="stat-number" id="todayConversations">-</div>
                        <div class="stat-label">今日对话</div>
                    </div>
                </div>
            </section>
            
            <!-- 图表区域 -->
            <section class="charts-grid">
                <div class="chart-container">
                    <h3>知识库分布</h3>
                    <canvas id="knowledgeDistributionChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>群组活跃度</h3>
                    <canvas id="groupActivityChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>标签使用频率</h3>
                    <canvas id="tagFrequencyChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>司机操守统计</h3>
                    <canvas id="driverConductChart"></canvas>
                </div>
            </section>
            
            <!-- 详细信息 -->
            <section class="details-section">
                <div class="detail-panel">
                    <h3>最新处理事件</h3>
                    <div id="recentEvents" class="event-list"></div>
                </div>
                
                <div class="detail-panel">
                    <h3>热门标签</h3>
                    <div id="topTags" class="tag-cloud"></div>
                </div>
            </section>
        </main>
        
        <footer class="footer">
            <p>数据更新时间: <span id="lastUpdate">-</span></p>
        </footer>
    </div>
    
    <script src="js/dashboard.js"></script>
</body>
</html>
        """
    
    def _generate_network_html(self) -> str:
        """生成知识网络HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识网络可视化</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🕸️ 知识网络可视化</h1>
            <p>实体关联和引用关系图谱</p>
        </header>
        
        <nav class="navigation">
            <a href="dashboard.html" class="nav-link">📊 总览</a>
            <a href="knowledge_network.html" class="nav-link active">🕸️ 知识网络</a>
            <a href="trend_analysis.html" class="nav-link">📈 趋势分析</a>
        </nav>
        
        <main class="main-content">
            <div class="network-controls">
                <label>
                    节点类型过滤:
                    <select id="nodeTypeFilter">
                        <option value="all">全部</option>
                        <option value="driver_id">司机ID</option>
                        <option value="order_id">订单号</option>
                        <option value="amount">金额</option>
                    </select>
                </label>
                
                <label>
                    最小连接数:
                    <input type="range" id="minConnections" min="1" max="10" value="1">
                    <span id="minConnectionsValue">1</span>
                </label>
            </div>
            
            <div id="networkVisualization" class="network-container"></div>
            
            <div class="network-info">
                <div class="info-panel">
                    <h3>网络统计</h3>
                    <div id="networkStats"></div>
                </div>
                
                <div class="info-panel">
                    <h3>选中节点信息</h3>
                    <div id="nodeInfo">点击节点查看详细信息</div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="js/network.js"></script>
</body>
</html>
        """
    
    def _generate_trends_html(self) -> str:
        """生成趋势分析HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趋势分析</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>📈 趋势分析</h1>
            <p>时间序列数据和趋势变化</p>
        </header>
        
        <nav class="navigation">
            <a href="dashboard.html" class="nav-link">📊 总览</a>
            <a href="knowledge_network.html" class="nav-link">🕸️ 知识网络</a>
            <a href="trend_analysis.html" class="nav-link active">📈 趋势分析</a>
        </nav>
        
        <main class="main-content">
            <section class="trends-grid">
                <div class="chart-container large">
                    <h3>对话量时间趋势</h3>
                    <canvas id="conversationTrendChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>每日活跃度</h3>
                    <canvas id="dailyActivityChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <h3>每周分布</h3>
                    <canvas id="weeklyDistributionChart"></canvas>
                </div>
                
                <div class="chart-container large">
                    <h3>知识创建趋势</h3>
                    <canvas id="knowledgeCreationChart"></canvas>
                </div>
            </section>
        </main>
    </div>
    
    <script src="js/trends.js"></script>
</body>
</html>
        """
    
    def _generate_static_assets(self):
        """生成CSS和JS静态资源文件"""
        # 生成完整的CSS文件
        css_content = """
/* 知识库可视化仪表盘样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.navigation {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
}

.nav-link {
    padding: 12px 24px;
    background: rgba(255,255,255,0.1);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.nav-link:hover, .nav-link.active {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.main-content {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.chart-container {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-3px);
}

.chart-container h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3rem;
}

.chart-container.large {
    grid-column: span 2;
}

.details-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

.detail-panel {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.detail-panel h3 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.event-list {
    max-height: 300px;
    overflow-y: auto;
}

.event-item {
    padding: 10px;
    border-left: 3px solid #667eea;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-item {
    padding: 5px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: transform 0.2s ease;
}

.tag-item:hover {
    transform: scale(1.05);
}

.footer {
    text-align: center;
    margin-top: 40px;
    color: rgba(255,255,255,0.8);
}

/* 网络可视化样式 */
.network-container {
    background: white;
    border-radius: 15px;
    height: 600px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.network-controls {
    background: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.network-controls label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.network-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-panel {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

/* 趋势分析样式 */
.trends-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .navigation {
        flex-direction: column;
        align-items: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-container.large {
        grid-column: span 1;
    }

    .header h1 {
        font-size: 2rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: absolute;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 0.9rem;
    pointer-events: none;
    z-index: 1000;
}
        """

        css_path = self.static_path / "css" / "dashboard.css"
        with open(css_path, 'w', encoding='utf-8') as f:
            f.write(css_content)

        # 生成仪表盘JavaScript文件
        dashboard_js = """
// 知识库仪表盘JavaScript
class KnowledgeDashboard {
    constructor() {
        this.charts = {};
        this.data = {};
        this.init();
    }

    async init() {
        try {
            await this.loadData();
            this.updateStats();
            this.createCharts();
            this.updateDetails();
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('数据加载失败，请检查数据文件是否存在');
        }
    }

    async loadData() {
        const dataFiles = [
            'dashboard_stats',
            'knowledge_distribution',
            'time_trends',
            'tag_analysis',
            'entity_statistics',
            'driver_conduct_stats'
        ];

        for (const file of dataFiles) {
            try {
                const response = await fetch(`data/${file}.json`);
                if (response.ok) {
                    this.data[file] = await response.json();
                } else {
                    console.warn(`无法加载 ${file}.json`);
                    this.data[file] = {};
                }
            } catch (error) {
                console.warn(`加载 ${file}.json 失败:`, error);
                this.data[file] = {};
            }
        }
    }

    updateStats() {
        const stats = this.data.dashboard_stats?.overview || {};

        this.updateElement('totalConversations', stats.total_conversations || 0);
        this.updateElement('totalKnowledge', stats.total_knowledge_items || 0);
        this.updateElement('totalEntities', stats.total_entities || 0);
        this.updateElement('todayConversations', stats.today_conversations || 0);

        // 更新最后更新时间
        const lastUpdate = this.data.dashboard_stats?.generated_at;
        if (lastUpdate) {
            const date = new Date(lastUpdate);
            this.updateElement('lastUpdate', date.toLocaleString('zh-CN'));
        }
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            if (typeof value === 'number') {
                this.animateNumber(element, value);
            } else {
                element.textContent = value;
            }
        }
    }

    animateNumber(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = currentValue.toLocaleString();

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    createCharts() {
        this.createKnowledgeDistributionChart();
        this.createGroupActivityChart();
        this.createTagFrequencyChart();
        this.createDriverConductChart();
    }

    createKnowledgeDistributionChart() {
        const ctx = document.getElementById('knowledgeDistributionChart');
        if (!ctx) return;

        const distribution = this.data.knowledge_distribution || {};
        const generalData = distribution.general_knowledge || {};
        const driverData = distribution.driver_conduct || {};

        this.charts.knowledgeDistribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['技术问题', '流程规范', 'FAQ', '违规案例', '行为规范', '处罚标准'],
                datasets: [{
                    data: [
                        generalData.technical || 0,
                        generalData.process || 0,
                        generalData.faq || 0,
                        driverData.violations || 0,
                        driverData.regulations || 0,
                        driverData.penalties || 0
                    ],
                    backgroundColor: [
                        '#3498db', '#2ecc71', '#f39c12',
                        '#e74c3c', '#9b59b6', '#e67e22'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    createGroupActivityChart() {
        const ctx = document.getElementById('groupActivityChart');
        if (!ctx) return;

        const groupData = this.data.dashboard_stats?.group_distribution || {};

        this.charts.groupActivity = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['群组2(客服)', '群组7(司机)', '群组8(部门)'],
                datasets: [{
                    label: '对话数量',
                    data: [
                        groupData.group_2 || 0,
                        groupData.group_7 || 0,
                        groupData.group_8 || 0
                    ],
                    backgroundColor: ['#3498db', '#e74c3c', '#2ecc71']
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createTagFrequencyChart() {
        const ctx = document.getElementById('tagFrequencyChart');
        if (!ctx) return;

        const tagData = this.data.tag_analysis?.top_tags || {};
        const labels = Object.keys(tagData).slice(0, 10);
        const values = labels.map(label => tagData[label]);

        this.charts.tagFrequency = new Chart(ctx, {
            type: 'horizontalBar',
            data: {
                labels: labels,
                datasets: [{
                    label: '使用频率',
                    data: values,
                    backgroundColor: '#667eea'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createDriverConductChart() {
        const ctx = document.getElementById('driverConductChart');
        if (!ctx) return;

        const conductData = this.data.driver_conduct_stats || {};
        const violationTypes = conductData.violation_types || {};

        this.charts.driverConduct = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: Object.keys(violationTypes),
                datasets: [{
                    data: Object.values(violationTypes),
                    backgroundColor: [
                        '#e74c3c', '#f39c12', '#9b59b6',
                        '#e67e22', '#1abc9c'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    updateDetails() {
        this.updateRecentEvents();
        this.updateTopTags();
    }

    updateRecentEvents() {
        const container = document.getElementById('recentEvents');
        if (!container) return;

        const timeline = this.data.processing_timeline?.events || [];
        const recentEvents = timeline.slice(-10);

        container.innerHTML = recentEvents.map(event => `
            <div class="event-item">
                <div style="font-weight: bold; color: #667eea;">
                    ${event.sender} - ${event.group_id}
                </div>
                <div style="font-size: 0.9rem; margin-top: 5px;">
                    ${event.content}
                </div>
                <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                    ${new Date(event.timestamp).toLocaleString('zh-CN')}
                </div>
            </div>
        `).join('');
    }

    updateTopTags() {
        const container = document.getElementById('topTags');
        if (!container) return;

        const topTags = this.data.tag_analysis?.top_tags || {};

        container.innerHTML = Object.entries(topTags)
            .slice(0, 20)
            .map(([tag, count]) => `
                <span class="tag-item" title="使用 ${count} 次">
                    ${tag}
                </span>
            `).join('');
    }

    showError(message) {
        const container = document.querySelector('.main-content');
        if (container) {
            container.innerHTML = `
                <div style="text-align: center; padding: 50px; color: #e74c3c;">
                    <h3>⚠️ ${message}</h3>
                    <p style="margin-top: 20px;">请确保已运行数据处理脚本生成可视化数据。</p>
                </div>
            `;
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new KnowledgeDashboard();
});
        """

        dashboard_js_path = self.static_path / "js" / "dashboard.js"
        with open(dashboard_js_path, 'w', encoding='utf-8') as f:
            f.write(dashboard_js)
