{"$schema": "http://json-schema.org/draft-07/schema#", "title": "知识库条目数据结构规范", "description": "定义知识库中每个条目的标准数据结构", "type": "object", "required": ["id", "title", "description", "category", "tags", "content", "metadata"], "properties": {"id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "description": "唯一标识符，使用字母、数字、下划线和连字符"}, "title": {"type": "string", "minLength": 1, "maxLength": 200, "description": "知识条目标题"}, "description": {"type": "string", "minLength": 1, "maxLength": 1000, "description": "详细描述"}, "category": {"type": "string", "enum": ["technical", "process", "faq", "regulations", "violations", "penalties"], "description": "主分类"}, "subcategory": {"type": "string", "description": "子分类，可选"}, "tags": {"type": "array", "items": {"type": "string", "pattern": "^[a-z_]+$"}, "uniqueItems": true, "description": "标签数组，使用小写字母和下划线"}, "content": {"type": "object", "required": ["summary", "details"], "properties": {"summary": {"type": "string", "minLength": 1, "maxLength": 500, "description": "简要总结"}, "details": {"type": "string", "minLength": 1, "description": "详细内容"}, "examples": {"type": "array", "items": {"type": "string"}, "description": "示例列表"}, "related_cases": {"type": "array", "items": {"type": "string"}, "description": "相关案例"}, "steps": {"type": "array", "items": {"type": "object", "properties": {"step": {"type": "integer"}, "description": {"type": "string"}, "notes": {"type": "string"}}, "required": ["step", "description"]}, "description": "操作步骤（适用于流程类知识）"}}}, "metadata": {"type": "object", "required": ["created_at", "updated_at", "version", "author"], "properties": {"created_at": {"type": "string", "format": "date-time", "description": "创建时间（ISO 8601格式）"}, "updated_at": {"type": "string", "format": "date-time", "description": "最后更新时间（ISO 8601格式）"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "版本号（语义化版本）"}, "author": {"type": "string", "description": "创建者"}, "last_editor": {"type": "string", "description": "最后编辑者"}, "source": {"type": "string", "description": "数据来源"}, "confidence_level": {"type": "string", "enum": ["high", "medium", "low"], "default": "medium", "description": "可信度等级"}, "review_status": {"type": "string", "enum": ["draft", "reviewed", "approved", "deprecated"], "default": "draft", "description": "审核状态"}, "expiry_date": {"type": "string", "format": "date", "description": "过期日期（可选）"}}}, "relationships": {"type": "object", "properties": {"related_items": {"type": "array", "items": {"type": "string"}, "description": "相关条目ID列表"}, "prerequisites": {"type": "array", "items": {"type": "string"}, "description": "前置条目ID列表"}, "follow_ups": {"type": "array", "items": {"type": "string"}, "description": "后续条目ID列表"}, "supersedes": {"type": "array", "items": {"type": "string"}, "description": "替代的旧条目ID列表"}}}, "driver_conduct_specific": {"type": "object", "description": "司机操守知识库专用字段", "properties": {"violation_type": {"type": "string", "enum": ["tardiness", "no_show", "service_attitude", "capability_mismatch", "safety_violation", "communication_issue"], "description": "违规类型"}, "severity_level": {"type": "string", "enum": ["minor", "moderate", "serious", "critical"], "description": "严重程度"}, "penalty_type": {"type": "string", "enum": ["warning", "financial_penalty", "service_suspension", "permanent_ban"], "description": "处罚类型"}, "penalty_details": {"type": "object", "properties": {"financial_amount": {"type": "number", "description": "经济处罚金额或比例"}, "suspension_days": {"type": "integer", "description": "暂停服务天数"}, "additional_requirements": {"type": "array", "items": {"type": "string"}, "description": "额外要求"}}}, "evidence_types": {"type": "array", "items": {"type": "string", "enum": ["screenshot", "audio_recording", "customer_complaint", "system_record", "witness_statement"]}, "description": "证据类型"}, "case_studies": {"type": "array", "items": {"type": "object", "properties": {"case_id": {"type": "string"}, "driver_info": {"type": "string"}, "incident_description": {"type": "string"}, "resolution": {"type": "string"}, "outcome": {"type": "string"}}, "required": ["case_id", "incident_description", "resolution"]}, "description": "案例研究"}}}}}