#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识网络构建器
负责构建基于标签和引用关系的知识网络图谱
"""

import logging
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter
import math


class NetworkBuilder:
    """知识网络构建器"""
    
    def __init__(self, config: Dict):
        """
        初始化网络构建器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger('NetworkBuilder')
        
        # 网络构建参数
        self.min_edge_weight = config.get('network', {}).get('min_edge_weight', 1)
        self.max_nodes = config.get('network', {}).get('max_nodes', 500)
        self.node_size_factor = config.get('network', {}).get('node_size_factor', 10)
    
    def build_network(self, conversations: List[Dict], entities: List[Dict], 
                     knowledge_items: List[Dict]) -> Dict[str, Any]:
        """
        构建完整的知识网络
        
        Args:
            conversations: 对话列表
            entities: 实体列表
            knowledge_items: 知识条目列表
            
        Returns:
            Dict: 网络图数据
        """
        self.logger.info("开始构建知识网络")
        
        # 构建不同类型的网络
        entity_network = self._build_entity_network(entities)
        tag_network = self._build_tag_network(conversations, knowledge_items)
        knowledge_network = self._build_knowledge_network(knowledge_items)
        reference_network = self._build_reference_network(conversations, entities)
        
        # 合并网络
        merged_network = self._merge_networks([
            entity_network,
            tag_network,
            knowledge_network,
            reference_network
        ])
        
        # 计算网络指标
        network_metrics = self._calculate_network_metrics(merged_network)
        
        # 应用布局算法
        positioned_network = self._apply_layout(merged_network)
        
        result = {
            'nodes': positioned_network['nodes'],
            'edges': positioned_network['edges'],
            'metrics': network_metrics,
            'metadata': {
                'total_nodes': len(positioned_network['nodes']),
                'total_edges': len(positioned_network['edges']),
                'network_types': ['entity', 'tag', 'knowledge', 'reference'],
                'generated_at': self._get_current_timestamp()
            }
        }
        
        self.logger.info(f"网络构建完成: {len(result['nodes'])} 节点, {len(result['edges'])} 边")
        return result
    
    def _build_entity_network(self, entities: List[Dict]) -> Dict[str, Any]:
        """构建实体网络"""
        nodes = {}
        edges = []
        
        # 按对话分组实体
        conversation_entities = defaultdict(list)
        for entity in entities:
            conv_id = entity.get('conversation_id')
            if conv_id:
                conversation_entities[conv_id].append(entity)
        
        # 创建实体节点
        entity_counts = Counter()
        for entity in entities:
            entity_key = f"{entity.get('type', '')}:{entity.get('value', '')}"
            entity_counts[entity_key] += 1
        
        for entity_key, count in entity_counts.items():
            entity_type, entity_value = entity_key.split(':', 1)
            
            nodes[entity_key] = {
                'id': entity_key,
                'type': 'entity',
                'entity_type': entity_type,
                'label': entity_value,
                'size': min(count * self.node_size_factor, 100),
                'count': count,
                'color': self._get_entity_color(entity_type)
            }
        
        # 创建共现边
        for conv_id, conv_entities in conversation_entities.items():
            entity_keys = [f"{e.get('type', '')}:{e.get('value', '')}" for e in conv_entities]
            
            for i, entity1 in enumerate(entity_keys):
                for entity2 in entity_keys[i+1:]:
                    edge_id = f"{entity1}--{entity2}"
                    
                    # 查找现有边或创建新边
                    existing_edge = next((e for e in edges if e['id'] == edge_id), None)
                    if existing_edge:
                        existing_edge['weight'] += 1
                    else:
                        edges.append({
                            'id': edge_id,
                            'source': entity1,
                            'target': entity2,
                            'type': 'co_occurrence',
                            'weight': 1
                        })
        
        return {'nodes': list(nodes.values()), 'edges': edges}
    
    def _build_tag_network(self, conversations: List[Dict], knowledge_items: List[Dict]) -> Dict[str, Any]:
        """构建标签网络"""
        nodes = {}
        edges = []
        
        # 收集所有标签
        tag_counts = Counter()
        tag_cooccurrences = defaultdict(int)
        
        # 从对话中收集标签
        for conv in conversations:
            conv_tags = [tag.get('value', '') for tag in conv.get('generated_tags', [])]
            
            for tag in conv_tags:
                if tag:
                    tag_counts[tag] += 1
            
            # 计算标签共现
            for i, tag1 in enumerate(conv_tags):
                for tag2 in conv_tags[i+1:]:
                    if tag1 and tag2:
                        pair = tuple(sorted([tag1, tag2]))
                        tag_cooccurrences[pair] += 1
        
        # 从知识条目中收集标签
        for item in knowledge_items:
            item_tags = item.get('tags', [])
            
            for tag in item_tags:
                tag_counts[tag] += 1
            
            # 计算标签共现
            for i, tag1 in enumerate(item_tags):
                for tag2 in item_tags[i+1:]:
                    pair = tuple(sorted([tag1, tag2]))
                    tag_cooccurrences[pair] += 1
        
        # 创建标签节点
        for tag, count in tag_counts.items():
            if count >= 2:  # 只包含出现次数>=2的标签
                nodes[tag] = {
                    'id': f"tag:{tag}",
                    'type': 'tag',
                    'label': tag,
                    'size': min(count * 5, 80),
                    'count': count,
                    'color': self._get_tag_color(tag)
                }
        
        # 创建标签共现边
        for (tag1, tag2), weight in tag_cooccurrences.items():
            if weight >= self.min_edge_weight and f"tag:{tag1}" in nodes and f"tag:{tag2}" in nodes:
                edges.append({
                    'id': f"tag:{tag1}--tag:{tag2}",
                    'source': f"tag:{tag1}",
                    'target': f"tag:{tag2}",
                    'type': 'tag_cooccurrence',
                    'weight': weight
                })
        
        return {'nodes': list(nodes.values()), 'edges': edges}
    
    def _build_knowledge_network(self, knowledge_items: List[Dict]) -> Dict[str, Any]:
        """构建知识条目网络"""
        nodes = []
        edges = []
        
        # 创建知识条目节点
        for item in knowledge_items:
            item_id = item.get('id', '')
            title = item.get('title', '')
            category = item.get('category', '')
            
            nodes.append({
                'id': f"knowledge:{item_id}",
                'type': 'knowledge',
                'label': title[:30] + '...' if len(title) > 30 else title,
                'category': category,
                'size': 30,
                'color': self._get_category_color(category)
            })
        
        # 基于标签相似性创建边
        for i, item1 in enumerate(knowledge_items):
            tags1 = set(item1.get('tags', []))
            
            for item2 in knowledge_items[i+1:]:
                tags2 = set(item2.get('tags', []))
                
                # 计算标签相似性
                intersection = tags1.intersection(tags2)
                union = tags1.union(tags2)
                
                if len(intersection) > 0 and len(union) > 0:
                    similarity = len(intersection) / len(union)
                    
                    if similarity >= 0.3:  # 相似性阈值
                        edges.append({
                            'id': f"knowledge:{item1.get('id')}--knowledge:{item2.get('id')}",
                            'source': f"knowledge:{item1.get('id')}",
                            'target': f"knowledge:{item2.get('id')}",
                            'type': 'knowledge_similarity',
                            'weight': similarity,
                            'shared_tags': list(intersection)
                        })
        
        return {'nodes': nodes, 'edges': edges}
    
    def _build_reference_network(self, conversations: List[Dict], entities: List[Dict]) -> Dict[str, Any]:
        """构建引用关系网络"""
        nodes = []
        edges = []
        
        # 创建对话节点（只包含重要对话）
        important_conversations = self._filter_important_conversations(conversations)
        
        for conv in important_conversations:
            conv_id = conv.get('id', '')
            sender = conv.get('sender', '')
            content = conv.get('content', '')
            
            nodes.append({
                'id': f"conversation:{conv_id}",
                'type': 'conversation',
                'label': f"{sender}: {content[:20]}...",
                'sender': sender,
                'size': 20,
                'color': '#95a5a6'
            })
        
        # 创建引用边（基于实体共现）
        conv_entities = defaultdict(set)
        for entity in entities:
            conv_id = entity.get('conversation_id')
            entity_key = f"{entity.get('type')}:{entity.get('value')}"
            if conv_id:
                conv_entities[conv_id].add(entity_key)
        
        conv_ids = list(conv_entities.keys())
        for i, conv1 in enumerate(conv_ids):
            for conv2 in conv_ids[i+1:]:
                shared_entities = conv_entities[conv1].intersection(conv_entities[conv2])
                
                if len(shared_entities) > 0:
                    edges.append({
                        'id': f"conversation:{conv1}--conversation:{conv2}",
                        'source': f"conversation:{conv1}",
                        'target': f"conversation:{conv2}",
                        'type': 'entity_reference',
                        'weight': len(shared_entities),
                        'shared_entities': list(shared_entities)
                    })
        
        return {'nodes': nodes, 'edges': edges}
    
    def _filter_important_conversations(self, conversations: List[Dict]) -> List[Dict]:
        """过滤重要对话"""
        # 基于内容长度和标签数量筛选重要对话
        important = []
        
        for conv in conversations:
            content = conv.get('content', '')
            tags = conv.get('generated_tags', [])
            
            # 重要性评分
            score = 0
            score += min(len(content) / 50, 5)  # 内容长度
            score += len(tags) * 2  # 标签数量
            
            # 关键词加分
            important_keywords = ['订单', '司机', '投诉', '处罚', '问题', '解决']
            for keyword in important_keywords:
                if keyword in content:
                    score += 3
            
            if score >= 5:  # 重要性阈值
                important.append(conv)
        
        # 限制数量
        return sorted(important, key=lambda x: len(x.get('content', '')), reverse=True)[:50]
    
    def _merge_networks(self, networks: List[Dict]) -> Dict[str, Any]:
        """合并多个网络"""
        all_nodes = {}
        all_edges = []
        
        for network in networks:
            # 合并节点
            for node in network.get('nodes', []):
                node_id = node['id']
                if node_id in all_nodes:
                    # 合并节点属性
                    existing_node = all_nodes[node_id]
                    existing_node['size'] = max(existing_node.get('size', 0), node.get('size', 0))
                else:
                    all_nodes[node_id] = node
            
            # 合并边
            all_edges.extend(network.get('edges', []))
        
        # 去重边
        unique_edges = {}
        for edge in all_edges:
            edge_id = edge['id']
            if edge_id in unique_edges:
                # 合并边权重
                unique_edges[edge_id]['weight'] += edge.get('weight', 1)
            else:
                unique_edges[edge_id] = edge
        
        # 过滤低权重边
        filtered_edges = [
            edge for edge in unique_edges.values()
            if edge.get('weight', 1) >= self.min_edge_weight
        ]
        
        return {
            'nodes': list(all_nodes.values()),
            'edges': filtered_edges
        }
    
    def _calculate_network_metrics(self, network: Dict) -> Dict[str, Any]:
        """计算网络指标"""
        nodes = network.get('nodes', [])
        edges = network.get('edges', [])
        
        # 基本指标
        num_nodes = len(nodes)
        num_edges = len(edges)
        
        # 度分布
        degree_count = defaultdict(int)
        for edge in edges:
            degree_count[edge['source']] += 1
            degree_count[edge['target']] += 1
        
        degrees = list(degree_count.values())
        avg_degree = sum(degrees) / len(degrees) if degrees else 0
        max_degree = max(degrees) if degrees else 0
        
        # 密度
        max_possible_edges = num_nodes * (num_nodes - 1) / 2
        density = num_edges / max_possible_edges if max_possible_edges > 0 else 0
        
        # 节点类型分布
        node_types = Counter(node.get('type', 'unknown') for node in nodes)
        
        return {
            'num_nodes': num_nodes,
            'num_edges': num_edges,
            'avg_degree': round(avg_degree, 2),
            'max_degree': max_degree,
            'density': round(density, 4),
            'node_types': dict(node_types)
        }
    
    def _apply_layout(self, network: Dict) -> Dict[str, Any]:
        """应用布局算法"""
        nodes = network.get('nodes', [])
        edges = network.get('edges', [])
        
        # 简单的力导向布局
        positioned_nodes = self._force_directed_layout(nodes, edges)
        
        return {
            'nodes': positioned_nodes,
            'edges': edges
        }
    
    def _force_directed_layout(self, nodes: List[Dict], edges: List[Dict]) -> List[Dict]:
        """力导向布局算法"""
        import random
        
        # 初始化位置
        for i, node in enumerate(nodes):
            angle = 2 * math.pi * i / len(nodes)
            radius = 200
            node['x'] = radius * math.cos(angle)
            node['y'] = radius * math.sin(angle)
        
        # 简化的力导向算法
        for iteration in range(50):
            # 计算斥力
            for i, node1 in enumerate(nodes):
                fx, fy = 0, 0
                
                for j, node2 in enumerate(nodes):
                    if i != j:
                        dx = node1['x'] - node2['x']
                        dy = node1['y'] - node2['y']
                        distance = math.sqrt(dx*dx + dy*dy) + 0.01
                        
                        # 斥力
                        force = 1000 / (distance * distance)
                        fx += force * dx / distance
                        fy += force * dy / distance
                
                node1['fx'] = fx
                node1['fy'] = fy
            
            # 计算引力（基于边）
            for edge in edges:
                source_node = next(n for n in nodes if n['id'] == edge['source'])
                target_node = next(n for n in nodes if n['id'] == edge['target'])
                
                dx = target_node['x'] - source_node['x']
                dy = target_node['y'] - source_node['y']
                distance = math.sqrt(dx*dx + dy*dy) + 0.01
                
                # 引力
                force = distance * 0.01 * edge.get('weight', 1)
                fx = force * dx / distance
                fy = force * dy / distance
                
                source_node['fx'] += fx
                source_node['fy'] += fy
                target_node['fx'] -= fx
                target_node['fy'] -= fy
            
            # 更新位置
            for node in nodes:
                node['x'] += node.get('fx', 0) * 0.1
                node['y'] += node.get('fy', 0) * 0.1
                
                # 清除力
                node['fx'] = 0
                node['fy'] = 0
        
        return nodes
    
    def _get_entity_color(self, entity_type: str) -> str:
        """获取实体类型对应的颜色"""
        colors = {
            'driver_id': '#e74c3c',
            'order_id': '#3498db',
            'phone_number': '#9b59b6',
            'amount': '#f39c12',
            'time_duration': '#2ecc71',
            'location': '#1abc9c',
            'vehicle_info': '#34495e'
        }
        return colors.get(entity_type, '#95a5a6')
    
    def _get_tag_color(self, tag: str) -> str:
        """获取标签对应的颜色"""
        # 基于标签内容确定颜色
        if any(word in tag for word in ['urgent', 'important', '紧急', '重要']):
            return '#e74c3c'
        elif any(word in tag for word in ['order', 'process', '订单', '流程']):
            return '#3498db'
        elif any(word in tag for word in ['driver', 'conduct', '司机', '操守']):
            return '#9b59b6'
        else:
            return '#2ecc71'
    
    def _get_category_color(self, category: str) -> str:
        """获取分类对应的颜色"""
        colors = {
            'technical': '#3498db',
            'process': '#2ecc71',
            'faq': '#f39c12',
            'violations': '#e74c3c',
            'regulations': '#9b59b6',
            'penalties': '#e67e22'
        }
        return colors.get(category, '#95a5a6')
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
