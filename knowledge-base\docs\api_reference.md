# 知识库系统API参考文档

## 📡 API概述

知识库系统提供了完整的Python API和Web API，支持知识条目的增删改查、搜索过滤和系统管理功能。

## 🐍 Python API

### KnowledgeManager 类

主要的知识库管理类，提供核心功能。

#### 初始化

```python
from knowledge_manager import KnowledgeManager

# 使用默认路径初始化
manager = KnowledgeManager()

# 指定知识库路径
manager = KnowledgeManager(base_path="/path/to/knowledge-base")
```

#### 基本操作方法

##### add_knowledge_item(data, category=None)

添加知识条目。

**参数**：
- `data` (Dict): 知识条目数据
- `category` (str, 可选): 分类 ("general" 或 "driver-conduct")

**返回值**：
- `bool`: 添加是否成功

**示例**：

```python
item_data = {
    "id": "example_item_001",
    "title": "示例条目",
    "description": "这是一个示例条目",
    "category": "process",
    "tags": ["example", "demo"],
    "content": {
        "summary": "示例摘要",
        "details": "详细内容"
    },
    "metadata": {
        "created_at": "2025-07-09T12:00:00Z",
        "updated_at": "2025-07-09T12:00:00Z",
        "version": "1.0.0",
        "author": "系统管理员"
    }
}

success = manager.add_knowledge_item(item_data)
```

##### get_knowledge_item(item_id)

获取知识条目。

**参数**：
- `item_id` (str): 条目ID

**返回值**：
- `Dict` 或 `None`: 知识条目数据

**示例**：

```python
item = manager.get_knowledge_item("example_item_001")
if item:
    print(f"标题: {item['title']}")
    print(f"描述: {item['description']}")
```

##### update_knowledge_item(item_id, updates)

更新知识条目。

**参数**：
- `item_id` (str): 条目ID
- `updates` (Dict): 更新数据

**返回值**：
- `bool`: 更新是否成功

**示例**：

```python
updates = {
    "title": "更新后的标题",
    "tags": ["updated", "example"]
}

success = manager.update_knowledge_item("example_item_001", updates)
```

##### delete_knowledge_item(item_id)

删除知识条目。

**参数**：
- `item_id` (str): 条目ID

**返回值**：
- `bool`: 删除是否成功

**示例**：

```python
success = manager.delete_knowledge_item("example_item_001")
```

##### search_knowledge(query, **filters)

搜索知识条目。

**参数**：
- `query` (str): 搜索查询
- `**filters`: 过滤条件

**过滤条件**：
- `category` (str): 分类过滤
- `tags` (List[str]): 标签过滤
- `confidence_level` (str): 可信度过滤
- `date_range` (Dict): 时间范围过滤

**返回值**：
- `List[Dict]`: 搜索结果列表

**示例**：

```python
# 基本搜索
results = manager.search_knowledge("订单处理")

# 带过滤条件的搜索
results = manager.search_knowledge(
    "司机违规",
    category="violations",
    tags=["tardiness", "serious"],
    confidence_level="high"
)

# 时间范围搜索
results = manager.search_knowledge(
    "系统更新",
    date_range={
        "start": "2025-07-01T00:00:00Z",
        "end": "2025-07-09T23:59:59Z"
    }
)
```

##### list_all_items(category=None)

列出所有知识条目。

**参数**：
- `category` (str, 可选): 分类过滤

**返回值**：
- `List[Dict]`: 条目列表

**示例**：

```python
# 列出所有条目
all_items = manager.list_all_items()

# 列出特定分类的条目
process_items = manager.list_all_items(category="process")
```

##### validate_all_items()

验证所有知识条目。

**返回值**：
- `Dict`: 验证结果，包含 'valid' 和 'invalid' 列表

**示例**：

```python
validation_result = manager.validate_all_items()
print(f"有效条目: {len(validation_result['valid'])}")
print(f"无效条目: {len(validation_result['invalid'])}")
```

### KnowledgeSearchEngine 类

搜索引擎类，提供高级搜索功能。

#### 主要方法

##### search(query="", limit=None, **filters)

执行搜索。

**示例**：

```python
from search_engine import KnowledgeSearchEngine
from pathlib import Path
import json

# 加载配置
with open('config/settings.json') as f:
    settings = json.load(f)

engine = KnowledgeSearchEngine(Path('data'), settings)

# 搜索
results = engine.search("订单处理", limit=10)
```

##### get_related_items(item_id, limit=5)

获取相关条目。

**示例**：

```python
related = engine.get_related_items("order_processing_workflow_001", limit=5)
```

##### get_statistics()

获取统计信息。

**示例**：

```python
stats = engine.get_statistics()
print(f"总条目数: {stats['total_items']}")
print(f"分类统计: {stats['categories']}")
```

### KnowledgeValidator 类

数据验证类。

#### 主要方法

##### validate_item(item)

验证单个条目。

**示例**：

```python
from validator import KnowledgeValidator
import json
import yaml

# 加载配置
with open('config/schema.json') as f:
    schema = json.load(f)
with open('config/tags.yaml') as f:
    tags_config = yaml.safe_load(f)

validator = KnowledgeValidator(schema, tags_config)

# 验证条目
is_valid = validator.validate_item(item_data)
if not is_valid:
    errors = validator.get_validation_errors()
    print("验证错误:", errors)
```

## 🌐 Web API

Web界面提供RESTful API接口。

### 基础URL

```
http://localhost:5000/api
```

### 端点列表

#### GET /api/search

搜索知识条目。

**查询参数**：
- `query` (str): 搜索查询
- `category` (str): 分类过滤
- `tags` (str): 标签过滤（逗号分隔）
- `confidence_level` (str): 可信度过滤

**响应**：

```json
[
  {
    "id": "order_processing_workflow_001",
    "title": "订单处理标准流程",
    "description": "客服团队处理订单的标准操作流程",
    "category": "process",
    "tags": ["order_processing", "customer_service"],
    "metadata": {
      "updated_at": "2025-07-09T10:00:00Z"
    }
  }
]
```

**示例**：

```bash
curl "http://localhost:5000/api/search?query=订单处理&category=process"
```

#### GET /api/list

列出所有知识条目。

**响应**：条目列表（格式同搜索接口）

**示例**：

```bash
curl "http://localhost:5000/api/list"
```

#### GET /api/stats

获取统计信息。

**响应**：

```json
{
  "total_items": 11,
  "categories": {
    "process": 4,
    "violations": 2,
    "regulations": 2,
    "penalties": 1,
    "faq": 1,
    "technical": 1
  },
  "tags": {
    "order_processing": 3,
    "customer_service": 4,
    "urgent": 2
  },
  "recent_updates": [
    {
      "id": "evidence_collection_standards_001",
      "title": "司机违规证据收集标准",
      "updated_at": "2025-07-09T12:30:00Z"
    }
  ]
}
```

**示例**：

```bash
curl "http://localhost:5000/api/stats"
```

#### POST /api/add

添加知识条目。

**请求体**：JSON格式的知识条目数据

**响应**：

```json
{
  "success": true
}
```

**示例**：

```bash
curl -X POST "http://localhost:5000/api/add" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "new_item_001",
    "title": "新条目",
    "description": "描述",
    "category": "process",
    "tags": ["new"],
    "content": {
      "summary": "摘要",
      "details": "详细内容"
    },
    "metadata": {
      "created_at": "2025-07-09T12:00:00Z",
      "updated_at": "2025-07-09T12:00:00Z",
      "version": "1.0.0",
      "author": "API用户"
    }
  }'
```

#### GET /api/item/{item_id}

获取单个知识条目。

**路径参数**：
- `item_id` (str): 条目ID

**响应**：完整的知识条目数据

**示例**：

```bash
curl "http://localhost:5000/api/item/order_processing_workflow_001"
```

## 🔧 命令行工具

### knowledge_manager.py

主要的命令行管理工具。

#### 基本用法

```bash
python knowledge_manager.py <command> [options]
```

#### 命令列表

##### init

初始化知识库目录结构。

```bash
python knowledge_manager.py init
```

##### add

添加知识条目。

```bash
python knowledge_manager.py add --file item.json [--category general|driver-conduct]
```

##### get

获取知识条目。

```bash
python knowledge_manager.py get --id item_id
```

##### search

搜索知识条目。

```bash
python knowledge_manager.py search --query "搜索词" [--category category] [--tags tag1,tag2]
```

##### list

列出所有条目。

```bash
python knowledge_manager.py list [--category category]
```

##### validate

验证所有条目。

```bash
python knowledge_manager.py validate
```

## 🚀 集成示例

### Python脚本集成

```python
#!/usr/bin/env python3
# 示例：批量导入知识条目

from knowledge_manager import KnowledgeManager
import json
import os

def batch_import(data_directory):
    manager = KnowledgeManager()
    
    for filename in os.listdir(data_directory):
        if filename.endswith('.json'):
            filepath = os.path.join(data_directory, filename)
            
            with open(filepath, 'r', encoding='utf-8') as f:
                item_data = json.load(f)
            
            success = manager.add_knowledge_item(item_data)
            print(f"导入 {filename}: {'成功' if success else '失败'}")

if __name__ == '__main__':
    batch_import('import_data/')
```

### Web应用集成

```javascript
// 示例：JavaScript前端集成

class KnowledgeBaseAPI {
    constructor(baseUrl = 'http://localhost:5000/api') {
        this.baseUrl = baseUrl;
    }
    
    async search(query, filters = {}) {
        const params = new URLSearchParams({ query, ...filters });
        const response = await fetch(`${this.baseUrl}/search?${params}`);
        return response.json();
    }
    
    async addItem(itemData) {
        const response = await fetch(`${this.baseUrl}/add`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(itemData)
        });
        return response.json();
    }
    
    async getStats() {
        const response = await fetch(`${this.baseUrl}/stats`);
        return response.json();
    }
}

// 使用示例
const api = new KnowledgeBaseAPI();

api.search('订单处理', { category: 'process' })
    .then(results => console.log('搜索结果:', results));
```

## 📝 错误处理

### Python API错误处理

```python
from knowledge_manager import KnowledgeManager

manager = KnowledgeManager()

try:
    item = manager.get_knowledge_item("non_existent_id")
    if item is None:
        print("条目不存在")
except Exception as e:
    print(f"获取条目时发生错误: {e}")
```

### Web API错误响应

```json
{
  "success": false,
  "error": "条目验证失败: 缺少必需字段 title"
}
```

---

*最后更新：2025-07-09*
*版本：1.0.0*
