{"confidence_thresholds": {"auto_approve": 0.9, "manual_review": 0.7, "reject": 0.5}, "processing_limits": {"max_messages_per_batch": 100, "max_api_calls_per_hour": 1000, "max_processing_time_minutes": 30}, "content_filters": {"min_message_length": 10, "max_message_length": 2000, "exclude_patterns": ["系统消息", "已删除", "撤回了一条消息", "Messages and calls are end-to-end encrypted", "消息和通话采用端到端加密"]}, "group_mappings": {"group_2": {"name": "客服团队", "knowledge_type": "general", "default_category": "process", "priority": "high"}, "group_7": {"name": "司机问题处理", "knowledge_type": "driver-conduct", "default_category": "violations", "priority": "high"}, "group_8": {"name": "各部门信息交流", "knowledge_type": "general", "default_category": "technical", "priority": "medium"}}, "quality_weights": {"completeness": 0.3, "consistency": 0.25, "usefulness": 0.25, "accuracy": 0.2}, "gemini_config": {"model": "gemini-pro", "temperature": 0.1, "max_output_tokens": 2048, "top_p": 0.8, "top_k": 40}, "notification_settings": {"email_notifications": false, "webhook_url": null, "notification_threshold": "manual_review"}, "backup_settings": {"auto_backup": true, "backup_interval_hours": 24, "max_backup_files": 30}}