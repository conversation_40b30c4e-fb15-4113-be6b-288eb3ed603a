#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库管理工具主模块
提供知识库的增删改查、搜索过滤和维护功能
"""

import os
import json
import yaml
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from search_engine import KnowledgeSearchEngine
from validator import KnowledgeValidator


class KnowledgeManager:
    """知识库管理器主类"""
    
    def __init__(self, base_path: str = None):
        """
        初始化知识库管理器
        
        Args:
            base_path: 知识库根目录路径
        """
        self.base_path = Path(base_path) if base_path else Path(__file__).parent.parent
        self.config_path = self.base_path / "config"
        self.data_path = self.base_path / "data"
        self.templates_path = self.base_path / "templates"
        
        # 加载配置
        self.settings = self._load_settings()
        self.schema = self._load_schema()
        self.tags_config = self._load_tags_config()
        
        # 初始化组件
        self.validator = KnowledgeValidator(self.schema, self.tags_config)
        self.search_engine = KnowledgeSearchEngine(self.data_path, self.settings)
        
        # 配置日志
        self._setup_logging()
    
    def _load_settings(self) -> Dict:
        """加载系统设置"""
        settings_file = self.config_path / "settings.json"
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logging.error(f"设置文件未找到: {settings_file}")
            return {}
    
    def _load_schema(self) -> Dict:
        """加载数据结构规范"""
        schema_file = self.config_path / "schema.json"
        try:
            with open(schema_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logging.error(f"数据结构文件未找到: {schema_file}")
            return {}
    
    def _load_tags_config(self) -> Dict:
        """加载标签配置"""
        tags_file = self.config_path / "tags.yaml"
        try:
            with open(tags_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logging.error(f"标签配置文件未找到: {tags_file}")
            return {}
    
    def _setup_logging(self):
        """配置日志系统"""
        log_config = self.settings.get('logging', {})
        log_level = getattr(logging, log_config.get('log_level', 'INFO').upper())
        
        logging.basicConfig(
            level=log_level,
            format=log_config.get('log_format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_config.get('log_file', 'knowledge_base.log'))
            ]
        )
    
    def init_knowledge_base(self):
        """初始化知识库目录结构"""
        directories = [
            self.data_path / "general" / "technical",
            self.data_path / "general" / "process", 
            self.data_path / "general" / "faq",
            self.data_path / "driver-conduct" / "regulations",
            self.data_path / "driver-conduct" / "violations",
            self.data_path / "driver-conduct" / "penalties"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logging.info(f"创建目录: {directory}")
        
        print("知识库初始化完成！")
    
    def add_knowledge_item(self, data: Dict, category: str = None) -> bool:
        """
        添加知识条目
        
        Args:
            data: 知识条目数据
            category: 分类 (general 或 driver-conduct)
            
        Returns:
            bool: 添加是否成功
        """
        try:
            # 验证数据格式
            if not self.validator.validate_item(data):
                logging.error("数据验证失败")
                return False
            
            # 确定保存路径
            if not category:
                category = "general" if data.get("category") in ["technical", "process", "faq"] else "driver-conduct"
            
            subcategory = data.get("category", "")
            file_path = self.data_path / category / subcategory / f"{data['id']}.json"
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"成功添加知识条目: {data['id']}")
            return True
            
        except Exception as e:
            logging.error(f"添加知识条目失败: {e}")
            return False
    
    def get_knowledge_item(self, item_id: str) -> Optional[Dict]:
        """
        获取知识条目
        
        Args:
            item_id: 条目ID
            
        Returns:
            Dict: 知识条目数据，如果不存在返回None
        """
        # 在所有目录中搜索
        for root, dirs, files in os.walk(self.data_path):
            for file in files:
                if file.endswith('.json') and file.startswith(item_id):
                    file_path = Path(root) / file
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if data.get('id') == item_id:
                                return data
                    except Exception as e:
                        logging.error(f"读取文件失败 {file_path}: {e}")
        
        return None
    
    def update_knowledge_item(self, item_id: str, updates: Dict) -> bool:
        """
        更新知识条目
        
        Args:
            item_id: 条目ID
            updates: 更新数据
            
        Returns:
            bool: 更新是否成功
        """
        # 获取现有数据
        current_data = self.get_knowledge_item(item_id)
        if not current_data:
            logging.error(f"未找到条目: {item_id}")
            return False
        
        # 合并更新数据
        current_data.update(updates)
        current_data['metadata']['updated_at'] = datetime.now().isoformat()
        
        # 增加版本号
        version_parts = current_data['metadata']['version'].split('.')
        version_parts[-1] = str(int(version_parts[-1]) + 1)
        current_data['metadata']['version'] = '.'.join(version_parts)
        
        # 验证更新后的数据
        if not self.validator.validate_item(current_data):
            logging.error("更新数据验证失败")
            return False
        
        # 删除旧文件并保存新文件
        self.delete_knowledge_item(item_id)
        return self.add_knowledge_item(current_data)
    
    def delete_knowledge_item(self, item_id: str) -> bool:
        """
        删除知识条目
        
        Args:
            item_id: 条目ID
            
        Returns:
            bool: 删除是否成功
        """
        # 查找并删除文件
        for root, dirs, files in os.walk(self.data_path):
            for file in files:
                if file.endswith('.json') and file.startswith(item_id):
                    file_path = Path(root) / file
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if data.get('id') == item_id:
                                os.remove(file_path)
                                logging.info(f"成功删除知识条目: {item_id}")
                                return True
                    except Exception as e:
                        logging.error(f"删除文件失败 {file_path}: {e}")
        
        logging.error(f"未找到要删除的条目: {item_id}")
        return False
    
    def search_knowledge(self, query: str, **filters) -> List[Dict]:
        """
        搜索知识条目
        
        Args:
            query: 搜索查询
            **filters: 过滤条件
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        return self.search_engine.search(query, **filters)
    
    def list_all_items(self, category: str = None) -> List[Dict]:
        """
        列出所有知识条目
        
        Args:
            category: 可选的分类过滤
            
        Returns:
            List[Dict]: 知识条目列表
        """
        items = []
        search_path = self.data_path / category if category else self.data_path
        
        for root, dirs, files in os.walk(search_path):
            for file in files:
                if file.endswith('.json'):
                    file_path = Path(root) / file
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            items.append({
                                'id': data.get('id'),
                                'title': data.get('title'),
                                'category': data.get('category'),
                                'tags': data.get('tags', []),
                                'updated_at': data.get('metadata', {}).get('updated_at')
                            })
                    except Exception as e:
                        logging.error(f"读取文件失败 {file_path}: {e}")
        
        return items
    
    def validate_all_items(self) -> Dict[str, List[str]]:
        """
        验证所有知识条目
        
        Returns:
            Dict: 验证结果，包含有效和无效条目列表
        """
        valid_items = []
        invalid_items = []
        
        for root, dirs, files in os.walk(self.data_path):
            for file in files:
                if file.endswith('.json'):
                    file_path = Path(root) / file
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if self.validator.validate_item(data):
                                valid_items.append(data.get('id'))
                            else:
                                invalid_items.append(data.get('id'))
                    except Exception as e:
                        logging.error(f"验证文件失败 {file_path}: {e}")
                        invalid_items.append(str(file_path))
        
        return {
            'valid': valid_items,
            'invalid': invalid_items
        }


def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='知识库管理工具')
    parser.add_argument('command', choices=['init', 'add', 'get', 'update', 'delete', 'search', 'list', 'validate'],
                       help='操作命令')
    parser.add_argument('--id', help='条目ID')
    parser.add_argument('--file', help='JSON文件路径')
    parser.add_argument('--query', help='搜索查询')
    parser.add_argument('--category', help='分类')
    parser.add_argument('--tags', help='标签（逗号分隔）')
    
    args = parser.parse_args()
    
    # 初始化管理器
    manager = KnowledgeManager()
    
    # 执行命令
    if args.command == 'init':
        manager.init_knowledge_base()
    
    elif args.command == 'add':
        if not args.file:
            print("错误: 需要指定 --file 参数")
            return
        
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if manager.add_knowledge_item(data, args.category):
                print(f"成功添加知识条目: {data.get('id')}")
            else:
                print("添加失败")
        except Exception as e:
            print(f"添加失败: {e}")
    
    elif args.command == 'get':
        if not args.id:
            print("错误: 需要指定 --id 参数")
            return
        
        item = manager.get_knowledge_item(args.id)
        if item:
            print(json.dumps(item, ensure_ascii=False, indent=2))
        else:
            print(f"未找到条目: {args.id}")
    
    elif args.command == 'search':
        if not args.query:
            print("错误: 需要指定 --query 参数")
            return
        
        filters = {}
        if args.category:
            filters['category'] = args.category
        if args.tags:
            filters['tags'] = args.tags.split(',')
        
        results = manager.search_knowledge(args.query, **filters)
        print(f"找到 {len(results)} 个结果:")
        for result in results:
            print(f"- {result.get('id')}: {result.get('title')}")
    
    elif args.command == 'list':
        items = manager.list_all_items(args.category)
        print(f"共 {len(items)} 个条目:")
        for item in items:
            print(f"- {item['id']}: {item['title']} [{item['category']}]")
    
    elif args.command == 'validate':
        results = manager.validate_all_items()
        print(f"有效条目: {len(results['valid'])}")
        print(f"无效条目: {len(results['invalid'])}")
        if results['invalid']:
            print("无效条目列表:")
            for item in results['invalid']:
                print(f"- {item}")


if __name__ == '__main__':
    main()
