# 知识库系统开发指南

## 🛠️ 开发环境设置

### 系统要求

- Python 3.8+
- 操作系统：Windows/Linux/macOS
- 内存：至少2GB可用内存
- 磁盘空间：至少1GB可用空间

### 依赖安装

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 安装依赖
pip install flask pyyaml jsonschema pathlib
```

### 开发工具推荐

- **IDE**: VS Code, PyCharm
- **版本控制**: Git
- **测试工具**: pytest
- **代码格式化**: black, flake8
- **文档生成**: sphinx

## 🏗️ 系统架构

### 核心组件

```
knowledge-base/
├── config/          # 配置管理
├── data/           # 数据存储
├── tools/          # 核心工具
│   ├── knowledge_manager.py    # 主管理器
│   ├── search_engine.py       # 搜索引擎
│   ├── validator.py           # 数据验证
│   └── web_interface.py       # Web界面
├── templates/      # 数据模板
└── docs/          # 文档
```

### 设计模式

- **单例模式**: KnowledgeManager类确保配置一致性
- **策略模式**: 不同的搜索和验证策略
- **观察者模式**: 数据变更通知机制
- **工厂模式**: 知识条目创建

### 数据流

```
用户输入 → 验证器 → 管理器 → 存储层
                ↓
搜索引擎 ← 索引层 ← 数据层
```

## 🔧 核心模块开发

### 1. 扩展数据验证器

添加新的验证规则：

```python
# validator.py 扩展示例

class CustomValidator(KnowledgeValidator):
    def validate_custom_field(self, item: Dict) -> bool:
        """自定义字段验证"""
        custom_field = item.get('custom_field')
        
        if custom_field:
            # 添加自定义验证逻辑
            if not self._validate_custom_format(custom_field):
                self.errors.append("自定义字段格式错误")
                return False
        
        return True
    
    def _validate_custom_format(self, value: str) -> bool:
        """自定义格式验证"""
        # 实现具体的验证逻辑
        return True
```

### 2. 扩展搜索引擎

添加新的搜索算法：

```python
# search_engine.py 扩展示例

class AdvancedSearchEngine(KnowledgeSearchEngine):
    def semantic_search(self, query: str, limit: int = 20) -> List[Dict]:
        """语义搜索"""
        # 实现语义搜索算法
        # 可以集成词向量、BERT等技术
        pass
    
    def recommendation_search(self, user_history: List[str]) -> List[Dict]:
        """基于用户历史的推荐搜索"""
        # 实现推荐算法
        pass
```

### 3. 添加新的存储后端

支持数据库存储：

```python
# storage_backend.py 新模块

from abc import ABC, abstractmethod

class StorageBackend(ABC):
    @abstractmethod
    def save_item(self, item: Dict) -> bool:
        pass
    
    @abstractmethod
    def load_item(self, item_id: str) -> Dict:
        pass
    
    @abstractmethod
    def delete_item(self, item_id: str) -> bool:
        pass

class DatabaseBackend(StorageBackend):
    def __init__(self, connection_string: str):
        # 初始化数据库连接
        pass
    
    def save_item(self, item: Dict) -> bool:
        # 实现数据库保存逻辑
        pass
```

## 🧪 测试开发

### 单元测试

创建测试文件：

```python
# tests/test_knowledge_manager.py

import unittest
import tempfile
import json
from pathlib import Path
from knowledge_manager import KnowledgeManager

class TestKnowledgeManager(unittest.TestCase):
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.manager = KnowledgeManager(self.temp_dir)
        
        # 创建测试数据
        self.test_item = {
            "id": "test_item_001",
            "title": "测试条目",
            "description": "这是一个测试条目",
            "category": "process",
            "tags": ["test"],
            "content": {
                "summary": "测试摘要",
                "details": "测试详细内容"
            },
            "metadata": {
                "created_at": "2025-07-09T12:00:00Z",
                "updated_at": "2025-07-09T12:00:00Z",
                "version": "1.0.0",
                "author": "测试用户"
            }
        }
    
    def test_add_knowledge_item(self):
        """测试添加知识条目"""
        result = self.manager.add_knowledge_item(self.test_item)
        self.assertTrue(result)
        
        # 验证条目是否正确保存
        retrieved_item = self.manager.get_knowledge_item("test_item_001")
        self.assertIsNotNone(retrieved_item)
        self.assertEqual(retrieved_item['title'], "测试条目")
    
    def test_search_knowledge(self):
        """测试搜索功能"""
        # 先添加测试数据
        self.manager.add_knowledge_item(self.test_item)
        
        # 执行搜索
        results = self.manager.search_knowledge("测试")
        self.assertGreater(len(results), 0)
        self.assertEqual(results[0]['id'], "test_item_001")
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)

if __name__ == '__main__':
    unittest.main()
```

### 集成测试

```python
# tests/test_integration.py

import unittest
import requests
import subprocess
import time
import signal
import os

class TestWebInterface(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """启动Web服务"""
        cls.web_process = subprocess.Popen(
            ['python', 'web_interface.py'],
            cwd='../tools'
        )
        time.sleep(2)  # 等待服务启动
    
    @classmethod
    def tearDownClass(cls):
        """停止Web服务"""
        cls.web_process.terminate()
        cls.web_process.wait()
    
    def test_api_search(self):
        """测试搜索API"""
        response = requests.get('http://localhost:5000/api/search?query=订单')
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIsInstance(data, list)
    
    def test_api_stats(self):
        """测试统计API"""
        response = requests.get('http://localhost:5000/api/stats')
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn('total_items', data)
        self.assertIn('categories', data)
```

### 性能测试

```python
# tests/test_performance.py

import time
import unittest
from knowledge_manager import KnowledgeManager

class TestPerformance(unittest.TestCase):
    def setUp(self):
        self.manager = KnowledgeManager()
    
    def test_search_performance(self):
        """测试搜索性能"""
        start_time = time.time()
        
        # 执行多次搜索
        for i in range(100):
            results = self.manager.search_knowledge("订单处理")
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 100
        
        # 断言平均搜索时间小于100ms
        self.assertLess(avg_time, 0.1)
        print(f"平均搜索时间: {avg_time:.3f}秒")
```

## 🚀 部署指南

### 开发环境部署

```bash
# 克隆代码
git clone <repository_url>
cd knowledge-base

# 安装依赖
pip install -r requirements.txt

# 初始化知识库
python tools/knowledge_manager.py init

# 启动开发服务器
python tools/web_interface.py
```

### 生产环境部署

使用Gunicorn部署：

```bash
# 安装Gunicorn
pip install gunicorn

# 创建WSGI应用
# wsgi.py
from tools.web_interface import app
if __name__ == "__main__":
    app.run()

# 启动生产服务器
gunicorn -w 4 -b 0.0.0.0:5000 wsgi:app
```

使用Docker部署：

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY . /app

RUN pip install -r requirements.txt

EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "wsgi:app"]
```

### 配置管理

```python
# config_manager.py
import os
import json

class ConfigManager:
    def __init__(self):
        self.env = os.getenv('ENVIRONMENT', 'development')
        self.config = self._load_config()
    
    def _load_config(self):
        config_file = f'config/{self.env}.json'
        with open(config_file) as f:
            return json.load(f)
    
    def get(self, key, default=None):
        return self.config.get(key, default)
```

## 📊 监控和日志

### 日志配置

```python
# logging_config.py
import logging
import logging.handlers

def setup_logging():
    logger = logging.getLogger('knowledge_base')
    logger.setLevel(logging.INFO)
    
    # 文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        'logs/knowledge_base.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    
    # 格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
```

### 性能监控

```python
# monitoring.py
import time
import functools
import logging

def monitor_performance(func):
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logging.info(f"{func.__name__} 执行时间: {execution_time:.3f}秒")
        
        return result
    return wrapper

# 使用示例
@monitor_performance
def search_knowledge(self, query):
    # 搜索逻辑
    pass
```

## 🔄 持续集成

### GitHub Actions配置

```yaml
# .github/workflows/ci.yml
name: CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run tests
      run: |
        pytest tests/ --cov=tools/
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## 📝 代码规范

### 代码风格

使用Black进行代码格式化：

```bash
pip install black
black tools/ tests/
```

使用flake8进行代码检查：

```bash
pip install flake8
flake8 tools/ tests/
```

### 文档字符串规范

```python
def search_knowledge(self, query: str, **filters) -> List[Dict]:
    """
    搜索知识条目
    
    Args:
        query (str): 搜索查询字符串
        **filters: 过滤条件
            - category (str): 分类过滤
            - tags (List[str]): 标签过滤
            - confidence_level (str): 可信度过滤
    
    Returns:
        List[Dict]: 搜索结果列表，每个元素包含知识条目的基本信息
    
    Raises:
        ValueError: 当查询参数无效时
        
    Example:
        >>> manager = KnowledgeManager()
        >>> results = manager.search_knowledge("订单处理", category="process")
        >>> print(len(results))
        3
    """
```

---

*最后更新：2025-07-09*  
*版本：1.0.0*
